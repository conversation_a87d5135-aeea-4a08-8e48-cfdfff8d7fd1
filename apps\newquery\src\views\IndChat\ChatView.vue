<template>
    <div
        class="flex h-[calc(100vh-56px)]"
        v-loading="appLoading"
        element-loading-text="应用加载中..."
    >
        <!-- 工作台 - 左侧 2/3 -->
        <div
            v-show="!appLoading"
            class="el-main-left !rounded-none !shadow-none border flex flex-col !p-0 h-[calc(100vh-60px)] overflow-hidden bg-white"
        >
            <div
                class="el-main-left-top flex justify-between items-center flex-shrink-0 py-6 px-5 border-solid border-0 border-b-[1px] border-[#e5e5e5]"
            >
                <div class="flex items-center">
                    <span class="text-xl font-semibold">工作台</span>
                </div>
            </div>
            <div
                class="el-main-left-bottom mt-3 flex flex-col px-6 h-[calc(100%-80px)] overflow-hidden"
            >
                <!-- 工作台内容 -->
                <component
                    v-if="rightBottomComponent"
                    :is="rightBottomComponent"
                    :key="rightBottomComponentKey"
                    :data="rightBottomData"
                    @update:data="(newData) => (rightBottomData = newData)"
                    class="flex-1"
                />
                <!-- 默认应用组件 -->

                <component
                    v-else-if="defaultAppComponent"
                    :is="defaultAppComponent"
                    :key="defaultAppComponentKey"
                    :data="defaultAppData"
                    @update:data="(newData) => (defaultAppData = newData)"
                    :is-input-focused="isInputFocused"
                    class="flex-1"
                />
                <!-- 空状态 -->
                <div v-else class="w-full flex justify-center mt-[188px]">
                    <div
                        class="w-[144px] h-[110px] flex flex-col items-center justify-center"
                    >
                        <!-- <img
                            src="@/assets/admin/file-quesheng.png"
                            class="w-[85px] h-[85px]"
                        />
                        <div
                            class="font18 font-zhongcu"
                            style="color: #666666; width: 10em"
                        >
                            本轮会话暂无内容输出
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
        <!-- 聊天区域 - 右侧 1/3 -->
        <div
            v-show="!appLoading"
            class="el-main-right relative border-0 border-[#E5E5E5] border-solid border-l-[1px] h-[calc(100vh-60px)]"
        >
            <!-- 聊天头部 -->
            <ChatHeader
                :app="currentApp"
                @new-chat="handleNewChat"
                @history="handleHistory"
                :disabled="appLoading"
            />
            <div class="container container-message" style="padding-top: 60px">
                <div class="container-center">
                    <div
                        class="conatiner-message-div flex w-full h-full flex-col justify-between"
                    >
                        <div
                            ref="scrollContainer"
                            class="taskmessage-div overflow-y-auto scrollbar-hide"
                            style="max-height: calc(85vh - 80px)"
                            @scroll="handleScroll"
                        >
                            <div ref="innerRef">
                                <!-- 应用的开场白 -->
                                <div v-if="taskmesssageList.length <= 0">
                                    <!-- 新版开场白 -->
                                    <NewOpeningStatement
                                        v-if="
                                            globalConfig.chat
                                                ?.useNewOpeningStatement
                                        "
                                        :app="currentApp"
                                        @question-click="
                                            handleSuggestedQuestion
                                        "
                                    />
                                    <!-- 旧版开场白 -->
                                    <div v-else class="opening-statement-div">
                                        <div>{{ displayedText1 }}</div>
                                        <div>{{ displayedText2 }}</div>
                                    </div>
                                </div>
                                <!-- 聊天内容 -->
                                <div v-else class="mt-[65px]">
                                    <template
                                        v-for="(
                                            message, index
                                        ) in taskmesssageList"
                                        :key="index"
                                    >
                                        <template
                                            v-if="message.role === 'user'"
                                        >
                                            <!-- 普通右侧模式 -->
                                            <div
                                                v-if="
                                                    globalConfig.chat
                                                        ?.userMessageStyle ===
                                                    'normal'
                                                "
                                                class="border-b border-gray-200"
                                            >
                                                <div
                                                    class="flex items-start justify-end gap-3"
                                                >
                                                    <!-- 消息内容 -->
                                                    <div
                                                        class="max-w-[calc(100%-80px)] user-message-bubble"
                                                        v-html="
                                                            renderMarkdown(
                                                                message.content
                                                            )
                                                        "
                                                    ></div>

                                                    <!-- 用户头像 -->
                                                    <div class="flex-shrink-0">
                                                        <UserAvatar
                                                            size="medium"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- 标题模式 -->
                                            <div
                                                v-else
                                                class="border-b border-gray-200 pb-5"
                                            >
                                                <div class="flex items-center">
                                                    <div
                                                        class="text-lg font-medium leading-8 whitespace-pre-wrap markdown-content user-message max-w-[96%] text-gray-800"
                                                        v-html="
                                                            renderMarkdown(
                                                                message.content
                                                            )
                                                        "
                                                    ></div>
                                                    <template
                                                        v-if="
                                                            message.favorite
                                                                ? message
                                                                      .favorite
                                                                      .id
                                                                : false
                                                        "
                                                    >
                                                        <el-tooltip
                                                            class="box-item"
                                                            effect="dark"
                                                            content="取消收藏"
                                                            placement="bottom"
                                                        >
                                                            <el-button
                                                                type=""
                                                                size="small"
                                                                text
                                                                @click="
                                                                    handleFavorite(
                                                                        message,
                                                                        '0'
                                                                    )
                                                                "
                                                                class="flex-shrink-0 w-[20px] h-[20px] ml-1"
                                                                :disabled="
                                                                    message.favoriteLoading
                                                                "
                                                            >
                                                                <template
                                                                    v-if="
                                                                        message.favoriteLoading
                                                                    "
                                                                >
                                                                    <el-icon
                                                                        class="is-loading"
                                                                        ><Loading
                                                                    /></el-icon>
                                                                </template>
                                                                <template
                                                                    v-else
                                                                >
                                                                    <CollectActiveIcon
                                                                        size="20"
                                                                    />
                                                                </template>
                                                            </el-button>
                                                        </el-tooltip>
                                                    </template>

                                                    <template v-else>
                                                        <template
                                                            v-if="
                                                                index !==
                                                                taskmesssageList.length -
                                                                    2
                                                            "
                                                        >
                                                            <el-tooltip
                                                                class="box-item"
                                                                effect="dark"
                                                                content="收藏指令"
                                                                placement="bottom"
                                                            >
                                                                <el-button
                                                                    type=""
                                                                    size="small"
                                                                    text
                                                                    @click="
                                                                        handleFavorite(
                                                                            message,
                                                                            '1'
                                                                        )
                                                                    "
                                                                    class="flex-shrink-0 w-[20px] h-[20px] ml-1"
                                                                    :disabled="
                                                                        message.favoriteLoading
                                                                    "
                                                                >
                                                                    <template
                                                                        v-if="
                                                                            message.favoriteLoading
                                                                        "
                                                                    >
                                                                        <el-icon
                                                                            class="is-loading"
                                                                            ><Loading
                                                                        /></el-icon>
                                                                    </template>
                                                                    <template
                                                                        v-else
                                                                    >
                                                                        <CollectIcon
                                                                            size="20"
                                                                        />
                                                                    </template>
                                                                </el-button>
                                                            </el-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <template
                                                                v-if="
                                                                    showOperate
                                                                "
                                                            >
                                                                <el-tooltip
                                                                    class="box-item"
                                                                    effect="dark"
                                                                    content="收藏指令"
                                                                    placement="bottom"
                                                                >
                                                                    <el-button
                                                                        type=""
                                                                        size="small"
                                                                        text
                                                                        @click="
                                                                            handleFavorite(
                                                                                message,
                                                                                '1'
                                                                            )
                                                                        "
                                                                        class="flex-shrink-0 w-[20px] h-[20px] ml-1"
                                                                        :disabled="
                                                                            message.favoriteLoading
                                                                        "
                                                                    >
                                                                        <template
                                                                            v-if="
                                                                                message.favoriteLoading
                                                                            "
                                                                        >
                                                                            <el-icon
                                                                                class="is-loading"
                                                                                ><Loading
                                                                            /></el-icon>
                                                                        </template>
                                                                        <template
                                                                            v-else
                                                                        >
                                                                            <CollectIcon
                                                                                size="20"
                                                                            />
                                                                        </template>
                                                                    </el-button>
                                                                </el-tooltip>
                                                            </template>
                                                        </template>
                                                    </template>
                                                </div>

                                                <div
                                                    class="text-sm text-gray-400 mt-3"
                                                    v-if="message.create_time"
                                                >
                                                    {{ message.create_time }}
                                                </div>
                                            </div>
                                        </template>

                                        <template
                                            v-else-if="
                                                message.role === 'assistant'
                                            "
                                        >
                                            <div class="mb-[0px]">
                                                <div
                                                    class="flex items-start gap-3 mt-5"
                                                >
                                                    <!-- AI头像 -->
                                                    <div class="flex-shrink-0">
                                                        <img
                                                            src="@/assets/images/home/<USER>"
                                                            alt=""
                                                            class="w-8 h-8"
                                                        />
                                                    </div>

                                                    <!-- 消息内容区域 -->
                                                    <div
                                                        class="flex-1 max-w-[calc(100%-80px)]"
                                                    >
                                                        <div
                                                            class="ai-message-bubble"
                                                            v-show="
                                                                message.content ||
                                                                message.thinkingContent
                                                            "
                                                        >
                                                            <div
                                                                class="text-lg"
                                                            >
                                                                <component
                                                                    v-if="
                                                                        message.topComponent
                                                                    "
                                                                    :is="
                                                                        message.topComponent
                                                                    "
                                                                    :key="
                                                                        message.topComponentKey
                                                                    "
                                                                    :data="
                                                                        message.topData
                                                                    "
                                                                    @update:data="
                                                                        (
                                                                            newData
                                                                        ) =>
                                                                            (message.topData =
                                                                                newData)
                                                                    "
                                                                />
                                                                <ThinkingBox
                                                                    v-if="
                                                                        message.thinkingContent
                                                                    "
                                                                    :content="
                                                                        message.thinkingContent
                                                                    "
                                                                    :completed="
                                                                        message.thinkingCompleted
                                                                    "
                                                                />
                                                                <div
                                                                    class="leading-8 text-base text-gray-700 wrapMessageContent"
                                                                    v-html="
                                                                        renderMarkdown(
                                                                            message.content
                                                                        )
                                                                    "
                                                                ></div>
                                                                <!-- 新增 mid-txt-bottom 区域组件 -->
                                                                <div
                                                                    class="mt-0"
                                                                    v-if="
                                                                        message.txtBottomComponent
                                                                    "
                                                                >
                                                                    <component
                                                                        :is="
                                                                            message.txtBottomComponent
                                                                        "
                                                                        :key="
                                                                            message.txtBottomComponentKey
                                                                        "
                                                                        :data="
                                                                            message.txtBottomData
                                                                        "
                                                                        @update:data="
                                                                            (
                                                                                newData
                                                                            ) =>
                                                                                (message.txtBottomData =
                                                                                    newData)
                                                                        "
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- 加载动画 -->
                                                        <img
                                                            src="@/assets/images/chat/loading.gif"
                                                            alt=""
                                                            class="h-2 mt-2"
                                                            v-if="
                                                                showLoading &&
                                                                index ==
                                                                    taskmesssageList.length -
                                                                        1
                                                            "
                                                        />

                                                        <!-- 操作按钮区域 -->
                                                        <div
                                                            class="border-b border-gray-200 pb-5 mt-1 flex justify-end items-center space-x-[0px]"
                                                            v-if="
                                                                index !==
                                                                    taskmesssageList.length -
                                                                        1 ||
                                                                showOperate
                                                            "
                                                        >
                                                            <el-tooltip
                                                                effect="dark"
                                                                content="复制"
                                                                placement="bottom"
                                                            >
                                                                <button
                                                                    class="p-2 border-none bg-transparent hover:bg-gray-100 rounded-md transition-colors"
                                                                    @click="
                                                                        copyContent(
                                                                            message.content
                                                                        )
                                                                    "
                                                                >
                                                                    <CopyIcon
                                                                        color="#A2A9B0"
                                                                        :size="
                                                                            18
                                                                        "
                                                                    />
                                                                </button>
                                                            </el-tooltip>
                                                            <el-tooltip
                                                                effect="dark"
                                                                content="重新生成"
                                                                placement="bottom"
                                                                v-if="
                                                                    message.role ===
                                                                        'assistant' &&
                                                                    index ===
                                                                        taskmesssageList.length -
                                                                            1
                                                                "
                                                            >
                                                                <button
                                                                    class="p-2 border-none bg-transparent hover:bg-gray-100 rounded-md transition-colors"
                                                                    @click="
                                                                        againSend(
                                                                            message.taskmessage_id
                                                                        )
                                                                    "
                                                                >
                                                                    <RefreshIcon
                                                                        color="#A2A9B0"
                                                                        :size="
                                                                            18
                                                                        "
                                                                    />
                                                                </button>
                                                            </el-tooltip>
                                                            <!-- <div
                                                                class="w-px h-5 bg-[#C1C7CD]"
                                                            ></div>
                                                            <el-tooltip
                                                                effect="dark"
                                                                content="赞"
                                                                placement="bottom"
                                                            >
                                                                <button
                                                                    class="p-2 border-none bg-transparent hover:bg-gray-100 rounded-md transition-colors w-9 h-9 flex items-center justify-center"
                                                                    @click="
                                                                        delCommentVote(
                                                                            message
                                                                        )
                                                                    "
                                                                    v-if="
                                                                        message.message_comment
                                                                            ? message
                                                                                  .message_comment
                                                                                  .vote ==
                                                                              '1'
                                                                            : false
                                                                    "
                                                                >
                                                                    <LikeActiveIcon
                                                                        color="#A2A9B0"
                                                                        size="20"
                                                                    />
                                                                </button>
                                                                <button
                                                                    class="p-2 border-none bg-transparent hover:bg-gray-100 rounded-md transition-colors w-9 h-9 flex items-center justify-center"
                                                                    @click="
                                                                        sendCommentVote(
                                                                            message,
                                                                            '1'
                                                                        )
                                                                    "
                                                                    v-else
                                                                >
                                                                    <LikeIcon
                                                                        color="#A2A9B0"
                                                                        size="20"
                                                                        style="
                                                                            width: 20px;
                                                                            height: 20px;
                                                                        "
                                                                    />
                                                                </button>
                                                            </el-tooltip>
                                                            <el-tooltip
                                                                effect="dark"
                                                                content="踩"
                                                                placement="bottom"
                                                            >
                                                                <button
                                                                    class="p-2 border-none bg-transparent hover:bg-gray-100 rounded-md transition-colors w-9 h-9 flex items-center justify-center"
                                                                    @click="
                                                                        delCommentVote(
                                                                            message
                                                                        )
                                                                    "
                                                                    v-if="
                                                                        message.message_comment
                                                                            ? message
                                                                                  .message_comment
                                                                                  .vote ==
                                                                              '0'
                                                                            : false
                                                                    "
                                                                >
                                                                    <NoLikeActiveIcon
                                                                        color="#A2A9B0"
                                                                        size="21"
                                                                    />
                                                                </button>
                                                                <button
                                                                    class="p-2 border-none bg-transparent hover:bg-gray-100 rounded-md transition-colors w-9 h-9 flex items-center justify-center"
                                                                    @click="
                                                                        sendCommentVote(
                                                                            message,
                                                                            '0'
                                                                        )
                                                                    "
                                                                    v-else
                                                                >
                                                                    <NoLikeIcon
                                                                        color="#A2A9B0"
                                                                        size="20"
                                                                    />
                                                                </button>
                                                            </el-tooltip> -->
                                                        </div>
                                                        <div
                                                            class="mt-[15px] flex items-center flex-wrap"
                                                            v-if="
                                                                message.bottomComponent
                                                            "
                                                        >
                                                            <component
                                                                :is="
                                                                    message.bottomComponent
                                                                "
                                                                :key="
                                                                    message.bottomComponentKey
                                                                "
                                                                :data="
                                                                    message.bottomData
                                                                "
                                                                @update:data="
                                                                    (newData) =>
                                                                        (message.bottomData =
                                                                            newData)
                                                                "
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </template>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="taskmessage-add">
                            <div
                                :style="{
                                    borderColor: focusIpt ? '' : '#d9d9d9',
                                }"
                            >
                                <div class="flex flex-1 items-center">
                                    <el-input
                                        v-model="userContent"
                                        :autosize="{ minRows: 3, maxRows: 6 }"
                                        type="textarea"
                                        placeholder="输入问题"
                                        resize="none"
                                        :disabled="!canWriteIpt || appLoading"
                                        maxlength="20000"
                                        @keydown="handleKeyDown"
                                        @focus="focusIpt = true"
                                        ref="inputTxtRef"
                                    />
                                </div>

                                <div class="flex items-end justify-between">
                                    <div>模型管理</div>
                                    <button
                                        :class="
                                            canSendMessage && !appLoading ? '' : 'send-btn-no'
                                        "
                                        :disabled="!canSendMessage || appLoading"
                                        @click="sendTaskmessageHandler"
                                        class="flex items-center justify-center"
                                    >
                                        <img
                                            src="@/assets/images/home/<USER>/<EMAIL>"
                                            alt=""
                                        />
                                    </button>
                                    <el-tooltip
                                        effect="dark"
                                        content="停止"
                                        placement="top"
                                    >
                                        <button
                                            class="p-0 border-none"
                                            style="background: none"
                                        >
                                            <StopChatIcon />
                                        </button>
                                    </el-tooltip>
                                </div>
                            </div>
                        </div> -->
                        <div
                            class="chat-input-box !pt-[8px] !pb-[8px]"
                            :style="{ borderColor: focusIpt ? '' : '#d9d9d9' }"
                        >
                            <div class="flex flex-1 items-center">
                                <el-input
                                    v-model="userContent"
                                    :autosize="{ minRows: 1, maxRows: 6 }"
                                    type="textarea"
                                    placeholder="有问题尽管问小A"
                                    resize="none"
                                    :disabled="!canWriteIpt || appLoading"
                                    maxlength="20000"
                                    @keydown="handleKeyDown"
                                    @focus="handleInputFocus"
                                    @blur="handleInputBlur"
                                    ref="inputTxtRef"
                                />
                            </div>
                            <div class="flex items-end">
                                <div class="h-[52px] flex items-center">
                                    <button
                                        :class="
                                            canSendMessage && !appLoading
                                                ? ''
                                                : 'send-btn-no'
                                        "
                                        :disabled="
                                            !canSendMessage || appLoading
                                        "
                                        @click="sendTaskmessageHandler"
                                        class="flex items-center justify-center"
                                    >
                                        <img
                                            src="@/assets/images/home/<USER>/<EMAIL>"
                                            alt=""
                                        />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="conatiner-tips-div text-[12px]">
                    内容由AI生成，无法确保真实准确，仅供参考，请阅读并遵守《AiALIGN用户协议》
                </div>
            </div>
        </div>

        <!-- 历史对话组件 -->
        <ChatHistory
            :is-visible="showHistoryPanel"
            :selected-topic-id="selectedTopicId"
            :app-id="selectedAppId"
            @close="handleCloseHistory"
            @select-history="handleSelectHistory"
        />
    </div>
</template>
<script setup name="ChatView">
import {
    onMounted,
    ref,
    watch,
    nextTick,
    onUnmounted,
    computed,
    shallowRef,
    markRaw,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css'; // 选择你喜欢的样式
import { ElMessage, ElLoading } from 'element-plus';
import { marked } from 'marked';
import { StreamMsgType } from '@/services/types/msg.ts';
import { getCurrentTime } from './utils';
import { encodeBase64AndUrl } from '@/utils/encoding';
import {
    sendTaskmessage,
    getTaskmessages,
    delTaskmessageApi,
    addMessageCommentApi,
    delMessageCommentApi,
    addFavoriteApi,
    delFavoriteApi,
    getTopicListByTime,
} from '@/services/intellido';

import {
    sendTaskmessageApi,
    getAppByIdApi,
    deleteMessageApi,
    getMessagesApi,
    getTopicsApi,
} from '@/services/turbine-api';

import { usePreviewComponents } from './hooks/usePreviewComponents';
import {
    resolveComponent,
    preloadCoreComponents,
    retryFailedComponent,
} from './components/componentRegister';
import { useThinking } from './hooks/useThinking'; // 引入思考钩子

import ThinkingBox from './components/ThinkingBox.vue'; // 引入思考框组件
import ChatHeader from './components/ChatHeader.vue'; // 引入聊天头部组件
import NewOpeningStatement from './components/NewOpeningStatement.vue'; // 引入新版开场白组件
import ChatHistory from './components/ChatHistory.vue'; // 引入历史对话组件
import UserAvatar from '@/components/UserAvatar.vue'; // 引入用户头像组件

import { useUserStore } from '@/store/modules/userInfo';
import { useConfig } from '@/hooks/useConfig'; // 引入配置钩子

import { useMessageBus, MessageType } from './hooks/useMessageBus';
import CopyIcon from '@/components/icons/chat/CopyIcon.vue';
import RefreshIcon from '@/components/icons/chat/RefreshIcon.vue';
import LikeIcon from '@/components/icons/chat/LikeIcon.vue';
import NoLikeIcon from '@/components/icons/chat/NoLikeIcon.vue';
import LikeActiveIcon from '@/components/icons/chat/LikeActiveIcon.vue';
import NoLikeActiveIcon from '@/components/icons/chat/NoLikeActiveIcon.vue';
import { debounce } from 'lodash-es';
import CollectIcon from '@/components/icons/favorite/CollectIcon.vue';
import CollectActiveIcon from '@/components/icons/favorite/CollectActiveIcon.vue';
import StopChatIcon from '@/components/icons/chat/StopChatIcon.vue';
import { Loading } from '@element-plus/icons-vue';

const userStore = useUserStore();
// 使用 computed 来确保响应性
const userInfo = computed(() => userStore.userInfo);

const route = useRoute();
const router = useRouter();
const { globalConfig } = useConfig(); // 获取全局配置
const canSendMessage = ref(false); //能否发送消息
const focusIpt = ref(false); //是否聚焦输入框
const userContent = ref(''); // 用户输入的内容
const canWriteIpt = ref(true); //默认可以输入内容
const taskmesssageList = ref([]); //上下文列表
const scrollContainer = ref(null); //滚动条滚动高度
const innerRef = ref(null);
const displayedText1 = ref('');
let currentIndex1 = 0;
let cursorInterval1 = null;
const displayedText2 = ref('');
let currentIndex2 = 0;
let cursorInterval2 = null;
const selectedAppId = ref(route.params.appId || route.query.app_id || null); //默认app的id
const selectQueryAppOpening = ref(null); //选中应用的开场白
const selectedTopicId = ref(route.query.topic_id || null); //默认选中的第一个上下文的id
const stream = ref(1); //发起聊天时的请求模式   0正常请求    1流模式请求
const showLoading = ref(false);
const showTool = ref(false);
const displayedTextLoading = ref(''); //等待ai回复时展示的文案
const showOperate = ref(true); //是否展示操作按钮
const markdownCache = ref(''); // 缓存不完整的Markdown数据
const curPreview = ref(); // 当前预览
const isRendering = ref(false); //ai内容是否还在渲染中
const inputTxtRef = ref(null);
const canScrollToBottom = ref(true); //ai回复内容时是否执行scrollToBottom函数，让内容一直至于底部
const jsonCache = ref(''); // 添加缓存存储不完整的JSON字符串
const currentAbortController = ref(null); // 当前请求的AbortController
const currentReader = ref(null); // 当前的流读取器
const isUserAborted = ref(false); // 标记是否为用户主动中断

// 历史对话相关状态
const showHistoryPanel = ref(false); // 是否显示历史对话面板

// 当前应用信息
const currentApp = ref({
    id: null,
    name: '',
    description: '',
    prologue: '',
    initial_component: null,
    file_icon: null,
    identifier: '',
});

// 应用加载状态
const appLoading = ref(false);

// 默认应用组件缓存
const defaultAppComponent = shallowRef(null);
const defaultAppComponentKey = ref(null);
const defaultAppData = ref(null);

const inputMsgQuery = route.query.question; //从其他页面带过来的用户输入的内容
const inputMsg = ref('');
if (inputMsgQuery) {
    try {
        inputMsg.value = decodeURIComponent(inputMsgQuery);
    } catch (e) {
        debugLogger.error('URL解码失败:', e);
        inputMsg.value = inputMsgQuery; // 如果解码失败，使用原始值
    }
}

if (route.params.appId || route.query.app_id) {
    selectedAppId.value = route.params.appId || route.query.app_id;
}

// 设置页面标题
const setPageTitle = (title) => {
    document.title = title;
};

// 如果需要在 Chat 页面中设置标题，使用新的编码方式
const updateTitle = (title) => {
    const encodedTitle = encodeBase64AndUrl(title);
    router.replace({
        query: { ...route.query, _t: encodedTitle },
    });
};

// 初始化思考钩子
const {
    processText,
    getThinkingContent,
    hasThinkingContent,
    resetThinking,
    isThinkingCompleted,
} = useThinking();

// 监听 taskmesssageList 的变化，并在变化后进行高亮处理
watch(taskmesssageList, async () => {
    // 初始化工作台
    for (let i = taskmesssageList.value.length - 1; i >= 0; i--) {
        const att_data = taskmesssageList.value[i].attachments_data;
        if (att_data && att_data.length > 0) {
            curPreview.value = att_data[att_data.length - 1];
            break;
        }
    }
    await nextTick();
    document.querySelectorAll('pre code').forEach((block) => {
        if (!block.dataset.highlighted) {
            hljs.highlightElement(block);
            block.dataset.highlighted = 'yes';
        }
    });
});

const showTitle1 = async () => {
    let fullText = `Hello，${userInfo.value.nickname}`;
    if (currentIndex1 < fullText.length) {
        displayedText1.value += fullText[currentIndex1];
        currentIndex1++;
        setTimeout(showTitle1, 100); // 调整速度
    } else {
        showTitle2();
        clearInterval(cursorInterval1);
    }
};

const showTitle2 = () => {
    let fullText = '';
    if (currentApp.value.prologue) {
        fullText = currentApp.value.prologue;
    } else if (selectQueryAppOpening.value) {
        fullText = selectQueryAppOpening.value;
    } else {
        fullText = `很高兴见到你，开始探索吧！`;
    }
    if (currentIndex2 < fullText.length) {
        displayedText2.value += fullText[currentIndex2];
        currentIndex2++;
        setTimeout(showTitle2, 100); // 调整速度
    } else {
        clearInterval(cursorInterval2);
    }
};

// 配置 marked 以使用 highlight.js
marked.setOptions({
    highlight: function (code, lang) {
        if (hljs.getLanguage(lang)) {
            return hljs.highlight(code, { language: lang }).value;
        } else {
            return hljs.highlightAuto(code).value;
        }
    },
});

// 渲染Markdown内容
const renderMarkdown = (content) => {
    // debugger;
    if (typeof content === 'object') {
        return marked(JSON.stringify(content, null, 2));
    }
    return marked(String(content));
};

// 添加调试工具类
const debugLogger = {
    enabled: false, // 默认关闭调试日志
    log(...args) {
        if (this.enabled) {
            console.log('[Stream]', ...args);
        }
    },
    error(...args) {
        if (this.enabled) {
            console.error('[Stream]', ...args);
        }
    },
};

// 可以通过以下方式开启/关闭调试日志
// debugLogger.enabled = true;

const parseStreamToJson = (dataStr) => {
    jsonCache.value += dataStr;

    const tryParseJson = (str) => {
        try {
            return JSON.parse(str);
        } catch (e) {
            return null;
        }
    };

    if (jsonCache.value.includes('}{')) {
        const parts = jsonCache.value.split('}{');
        const result = [];

        for (let i = 0; i < parts.length; i++) {
            let processedPart = parts[i];
            if (i === 0) {
                processedPart = processedPart + '}';
            } else if (i === parts.length - 1) {
                processedPart = '{' + processedPart;
            } else {
                processedPart = '{' + processedPart + '}';
            }

            const parsedJson = tryParseJson(processedPart);
            if (parsedJson) {
                result.push(parsedJson);
                if (i === parts.length - 1) {
                    jsonCache.value = '';
                }
            } else {
                debugLogger.log('JSON解析失败，保留在缓存中:', processedPart);
                if (i === parts.length - 1) {
                    jsonCache.value = processedPart;
                }
            }
        }

        return result.length > 0 ? result : [];
    } else {
        const parsedJson = tryParseJson(jsonCache.value);
        if (parsedJson) {
            jsonCache.value = '';
            return [parsedJson];
        }
        debugLogger.log('JSON解析失败，保留在缓存中:', jsonCache.value);
        return [];
    }
};

// 解析流数据
const parseStreamData = (orgData) => {
    try {
        // debugger;
        debugLogger.log('原始数据:', orgData);
        const itemList = parseStreamToJson(orgData);
        debugLogger.log('JSON解析后的数据:', itemList);
        const res = itemList
            .map((item) => {
                try {
                    const data = item;
                    if (data.error) {
                        debugLogger.log('发现错误数据:', data.error);
                        return {
                            topic_id: null,
                            content: [{ content: '' }],
                            taskmessage_id: '',
                            error: data.error,
                        };
                    } else if (data.topic_id) {
                        debugLogger.log('发现topic数据:', data);
                        const topic_id = data.topic_id;
                        selectedTopicId.value = topic_id;
                        const content = data.data || '';
                        const taskmessage_id =
                            data.taskmessage_id || data.message_id;
                        return {
                            topic_id,
                            content: [{ content }],
                            taskmessage_id,
                        };
                    } else {
                        if (data.uid === 'file-list-component') {
                            debugLogger.log('发现文件列表数据:', data);
                        }
                        data.content = data.data;
                        return data;
                    }
                } catch (error) {
                    debugLogger.error('解析单条消息失败:', error);
                    return null;
                }
            })
            .filter(Boolean);

        debugLogger.log('最终解析结果:', {
            数据条数: res.length,
            是否包含错误: res.length === 1 && res[0].error,
            是否包含Topic:
                res.length === 1 && typeof res[0].topic_id === 'number',
        });

        if (res.length === 1 && res[0].error) {
            return res[0];
        } else if (res.length === 1 && typeof res[0].topic_id === 'number') {
            return res[0];
        } else {
            // 检查res数组中是否有包含message_id和topic_id的数据
            const messageItem = res.find(
                (item) => item.taskmessage_id && item.topic_id
            );
            if (messageItem) {
                return {
                    topic_id: messageItem.topic_id,
                    taskmessage_id: messageItem.taskmessage_id,
                    content: res,
                };
            }
            return { topic_id: null, content: res };
        }
    } catch (error) {
        debugLogger.error('解析流数据失败:', error);
        return {
            topic_id: null,
            content: [{ content: '' }],
            taskmessage_id: '',
        };
    }
};

// 监听键盘事件
const handleKeyDown = (event) => {
    // 检查是否按下了Enter键
    if (event.key === 'Enter') {
        // 检查userContent是否为空
        if (userContent.value.trim() === '') {
            // 如果userContent为空，阻止默认行为
            event.preventDefault();
            return; // 不继续执行
        }
        // 检查是否同时按下了Shift键
        if (event.shiftKey || event.ctrlKey) {
            // 如果按下了Shift+Enter或ctrl+enter，执行换行操作
            userContent.value += '\n';
            event.preventDefault(); // 阻止默认的换行行为
        } else {
            // 如果只按下了Enter，发送文本
            sendTaskmessageHandler();
        }
    }
};

// 清空工作台
const clearWorkbench = () => {
    rightBottomComponent.value = null;
    // 注意：不清空默认应用组件，让它在没有其他内容时显示
};

// 条件性清空工作台 - 仅在有新组件内容时才清空
const conditionalClearWorkbench = () => {
    // 不在发送消息时清空工作台，而是等待有新组件内容时再清空
    // 这样可以保留上一次的工作台内容，直到有新的组件需要显示
};

// 获取应用历史记录
const fetchAppHistory = async (appId) => {
    if (!appId) return [];

    try {
        // 根据配置选择不同的接口
        if (globalConfig?.apps?.isIndChat) {
            // IndChat模式使用新接口
            const params = {
                page: 1,
                count: 100, // 获取更多历史记录
                app_id: appId,
            };

            const response = await getTopicsApi(params);
            return response.data?.data || [];
        } else {
            // 普通模式使用现有接口
            const response = await getTopicListByTime(
                appId,
                appId ? '' : 'default'
            );

            // 处理分组数据，按 today -> month -> year -> earlier 的顺序合并
            const data = response.data?.data || {};
            const mergedList = [];

            // 按顺序合并各个时间段的数据
            if (data.today && Array.isArray(data.today)) {
                mergedList.push(...data.today);
            }
            if (data.month && Array.isArray(data.month)) {
                mergedList.push(...data.month);
            }
            if (data.year && Array.isArray(data.year)) {
                mergedList.push(...data.year);
            }
            if (data.earlier && Array.isArray(data.earlier)) {
                mergedList.push(...data.earlier);
            }

            return mergedList;
        }
    } catch (error) {
        console.error('获取应用历史记录失败:', error);
        return [];
    }
};

// 获取应用信息
const fetchAppInfo = async (appId) => {
    if (!appId || appLoading.value) return;

    appLoading.value = true;
    try {
        const response = await getAppByIdApi(appId);
        const appData = response.data?.data;

        // 更新应用信息
        currentApp.value = {
            id: String(appData.id),
            name: appData.name || '',
            description: appData.description || '',
            prologue: appData.prologue || '',
            initial_component: appData.initial_component || null,
            file_icon: appData.file_icon || null,
            icon_url: appData.file_icon || '',
            identifier: appData.identifier || '',
        };

        // 当前应用id selectedAppId 直接赋值 identifier
        selectedAppId.value = currentApp.value.identifier;

        // 设置页面标题
        if (appData.name) {
            setPageTitle(appData.name);
        }

        // 初始化默认应用组件
        initDefaultAppComponent();

        // 获取应用历史记录并自动载入第一条
        await loadAppHistoryAndFirstTopic();

        console.log('应用信息加载成功:', currentApp.value);
    } catch (error) {
        console.error('获取应用信息失败:', error);
        ElMessage.error('获取应用信息失败');
    } finally {
        appLoading.value = false;
    }
};

// 加载应用历史记录并自动载入第一条
const loadAppHistoryAndFirstTopic = async () => {
    // 如果已经有选中的topic或者有URL参数指定的topic，不自动加载
    if (selectedTopicId.value || route.query.topic_id) {
        console.log('已有选中的topic，跳过自动加载历史记录');
        return;
    }

    // 如果有inputMsg参数，说明是从其他页面跳转过来要发送消息的，不自动加载历史记录
    if (inputMsg.value || route.query.question || route.query.content) {
        console.log('有待发送的消息，跳过自动加载历史记录');
        return;
    }

    // 如果是 turbine.blade_design 应用，不自动加载历史记录
    if (currentApp.value.identifier === 'turbine.blade_design') {
        console.log('turbine.blade_design应用不自动加载历史记录');
        return;
    }

    try {
        console.log('开始获取应用历史记录:', currentApp.value.identifier);
        const historyList = await fetchAppHistory(currentApp.value.identifier);

        if (historyList && historyList.length > 0) {
            console.log('找到历史记录，自动载入第一条:', historyList[0]);
            // 自动选择第一条历史记录，跳过加载层（因为应用信息加载层还在显示）
            const firstTopic = historyList[0];
            await handleSelectHistory(firstTopic.id, true);
        } else {
            console.log('没有找到历史记录');
        }
    } catch (error) {
        console.error('加载应用历史记录失败:', error);
    }
};

// 初始化默认应用组件
const initDefaultAppComponent = () => {
    if (currentApp.value.initial_component) {
        try {
            // 解析组件
            const component = resolveComponent(
                currentApp.value.initial_component
            );
            if (component) {
                defaultAppComponent.value = markRaw(component);
                defaultAppComponentKey.value = `${
                    currentApp.value.initial_component
                }-${Date.now()}`;

                // 构造默认应用数据
                defaultAppData.value = {
                    title: currentApp.value.name,
                    description: currentApp.value.description,
                    app: currentApp.value,
                };

                console.log('默认应用组件初始化成功:', {
                    component: currentApp.value.initial_component,
                    data: defaultAppData.value,
                });
            }
        } catch (error) {
            console.error('初始化默认应用组件失败:', error);
        }
    }
};

import { getAnswer } from './mock/index';
// 中断当前请求的函数
const abortCurrentRequest = (userInitiated = false) => {
    if (currentAbortController.value || currentReader.value) {
        isUserAborted.value = userInitiated;

        if (currentAbortController.value) {
            currentAbortController.value.abort();
            currentAbortController.value = null;
        }
        if (currentReader.value) {
            currentReader.value.cancel();
            currentReader.value = null;
        }
    }
};

// 发起聊天函数
const sendTaskmessageHandler = async () => {
    // 如果应用正在加载，阻止发送消息
    if (appLoading.value) {
        console.log('应用正在加载中，无法发送消息');
        return;
    }

    // 先中断之前的请求
    abortCurrentRequest();

    // 重置用户中断标记
    isUserAborted.value = false;

    canScrollToBottom.value = true; //可以执行scrollToBottom函数
    const userMessage = userContent.value;
    const userMessageObj = {
        role: 'user',
        content: userMessage,
        taskmessage_id: '',
        create_time: getCurrentTime(),
        favorite: null,
    };
    taskmesssageList.value.push(userMessageObj);
    // 清空缓存和状态
    markdownCache.value = '';
    resetThinking(); // 重置所有思考状态

    let aiMessage = {
        role: 'assistant',
        content: '',
        taskmessage_id: '',
        thinkingContent: '', // 添加思考内容字段
        thinkingCompleted: false, // 添加 thinkingCompleted 字段
        message_comment: {},
    };
    taskmesssageList.value.push(aiMessage);
    scrollToBottom();
    showTool.value = false;
    userContent.value = ''; // 清空输入框
    showLoading.value = true;
    showOperate.value = false; //将最后一条聊天内容的操作按钮隐藏，等聊天内容全展示后，在将按钮展示
    canWriteIpt.value = false; //不能输入内容
    isRendering.value = true;
    conditionalClearWorkbench(); //条件性清空工作台
    focusIpt.value = false; //失去焦点
    try {
        // 检查是否命中 mock 数据
        console.log('检查mock数据，用户输入:', userMessage);
        const mockAnswer = getAnswer(userMessage);
        console.log('mock匹配结果:', mockAnswer);
        if (mockAnswer) {
            // 模拟异步响应延迟
            await new Promise((resolve) => setTimeout(resolve, 2000));

            // 为模拟消息创建唯一ID
            const mockTaskMessageId = `mock-${Date.now()}`;
            // 重置思考状态
            resetThinking(mockTaskMessageId);

            if (Array.isArray(mockAnswer)) {
                // 处理包含组件的 mock 响应
                mockAnswer.forEach((item) => {
                    if (item.uid === 'default') {
                        // 处理普通文本消息，提取思考内容
                        const result = processText(
                            item.data || '',
                            mockTaskMessageId
                        );
                        aiMessage.content += result.normalText;

                        // 更新思考内容
                        if (hasThinkingContent(mockTaskMessageId)) {
                            aiMessage.thinkingContent =
                                getThinkingContent(mockTaskMessageId);
                            aiMessage.thinkingCompleted =
                                isThinkingCompleted(mockTaskMessageId);
                        }
                    } else if (!item.topic_id) {
                        // 更新预览组件
                        handlePreviewMessage(item);
                    }
                });
            } else {
                // 处理纯文本 mock 响应，同样提取思考内容
                const result = processText(mockAnswer, mockTaskMessageId);
                aiMessage.content = result.normalText;

                // 更新思考内容
                if (hasThinkingContent(mockTaskMessageId)) {
                    aiMessage.thinkingContent =
                        getThinkingContent(mockTaskMessageId);
                    aiMessage.thinkingCompleted =
                        isThinkingCompleted(mockTaskMessageId);
                }
            }

            // 生成一个模拟的 taskmessage_id
            aiMessage.taskmessage_id = mockTaskMessageId;
            userMessageObj.taskmessage_id = mockTaskMessageId;

            // 更新消息列表
            taskmesssageList.value = [...taskmesssageList.value];

            // 重置状态
            showLoading.value = false;
            canWriteIpt.value = true;
            showOperate.value = true;
            isRendering.value = false;

            scrollToBottom();
            return;
        } else {
            // 创建新的AbortController
            currentAbortController.value = new AbortController();

            // debugger;
            const response = await sendTaskmessageApi(
                userMessage,
                selectedAppId.value,
                selectedTopicId.value,
                selectedAppId.value ? '' : 'default',
                currentAbortController.value
            );
            if (stream.value == 1) {
                // 检查 response 是否包含流数据
                if (response && response.getReader) {
                    const reader = response.getReader();
                    currentReader.value = reader; // 保存reader引用
                    const decoder = new TextDecoder();
                    let done = false;
                    // 处理流数据
                    const processStream = async () => {
                        try {
                            while (!done) {
                                const { value, done: streamDone } =
                                    await reader.read();
                                done = streamDone;
                                if (value) {
                                    const chunk = decoder.decode(value, {
                                        stream: true,
                                    });
                                    debugLogger.log('[Stream] 收到新的数据块');

                                    let cacheTaskmessageId = '';

                                    const { content, taskmessage_id, error } =
                                        parseStreamData(chunk);

                                    if (taskmessage_id) {
                                        cacheTaskmessageId = taskmessage_id;
                                    }

                                    debugLogger.log(
                                        '[Stream] 解析后的数据结构:',
                                        {
                                            content: content,
                                            hasFileList:
                                                content.some &&
                                                content.some(
                                                    (item) =>
                                                        item.uid ===
                                                        'file-list-component'
                                                ),
                                            taskmessage_id: taskmessage_id,
                                            error: error,
                                        }
                                    );

                                    if (error == '1') {
                                        ElMessage.error('应用不存在');
                                        showLoading.value = false;
                                        return;
                                    }

                                    let text = '';
                                    content.map((item) => {
                                        debugLogger.log(
                                            '[Stream] 处理单条数据:',
                                            {
                                                type: item.type,
                                                uid: item.uid,
                                                topic_id: item.topic_id,
                                                area: item.area,
                                            }
                                        );

                                        if (
                                            item.type == StreamMsgType.StartTool
                                        ) {
                                            showLoading.value = true;
                                            showTool.value = true;
                                            displayedTextLoading.value =
                                                item.content;
                                        } else if (item.uid !== 'default') {
                                            if (!item.topic_id) {
                                                debugLogger.log(
                                                    '[Stream] 准备处理非默认消息:',
                                                    item
                                                );
                                                handlePreviewMessage(item);
                                            }
                                        } else {
                                            // 处理思考中文本
                                            const messageId =
                                                aiMessage.taskmessage_id;
                                            // debugger;
                                            // 处理文本，提取思考内容
                                            const result = processText(
                                                item.data || '',
                                                messageId
                                            );
                                            text += result.normalText;

                                            // 更新思考内容
                                            if (hasThinkingContent(messageId)) {
                                                aiMessage.thinkingContent =
                                                    getThinkingContent(
                                                        messageId
                                                    );
                                                aiMessage.thinkingCompleted =
                                                    isThinkingCompleted(
                                                        messageId
                                                    );
                                            }
                                        }
                                    });

                                    // 缓存不完整的Markdown数据
                                    markdownCache.value += text;
                                    try {
                                        // 尝试渲染Markdown
                                        aiMessage.content = markdownCache.value;
                                        if (taskmessage_id) {
                                            console.log(
                                                taskmessage_id,
                                                '====================='
                                            );
                                            aiMessage.taskmessage_id =
                                                taskmessage_id;
                                            // 更新用户消息的 taskmessage_id
                                            userMessageObj.taskmessage_id =
                                                taskmessage_id;
                                        }
                                        // 强制 Vue 重新渲染
                                        taskmesssageList.value = [
                                            ...taskmesssageList.value,
                                        ];
                                        scrollToBottom();
                                    } catch (error) {
                                        // 如果渲染失败，继续缓存
                                    }
                                }
                            }
                            showLoading.value = false;
                            canWriteIpt.value = true;

                            inputTxtRef.value.focus();
                            scrollToBottom();
                            taskmesssageList.value = [
                                ...taskmesssageList.value,
                            ];

                            // 流数据处理完成后，设置 showOperate 为 true
                            showOperate.value = true;
                            isRendering.value = false; //ai内容全部渲染完成
                        } catch (error) {
                            // 处理流读取过程中的错误（包括中断）
                            if (error.name === 'AbortError') {
                                debugLogger.log('流请求被中断');
                                // 如果是用户主动中断，不显示错误信息
                                if (isUserAborted.value) {
                                    debugLogger.log(
                                        '用户主动中断，不显示错误信息'
                                    );
                                    isUserAborted.value = false; // 重置标记
                                    return;
                                }
                            }
                            debugLogger.error('处理流数据失败:', error);
                            showLoading.value = false;
                            canWriteIpt.value = true;
                            // 只有在非用户主动中断的情况下才显示错误信息
                            if (!isUserAborted.value) {
                                taskmesssageList.value.pop();
                                taskmesssageList.value.push({
                                    role: 'assistant',
                                    content:
                                        '当前请求网络可能有问题，请重新发起对话',
                                });
                                scrollToBottom();
                            }
                            isUserAborted.value = false; // 重置标记
                        } finally {
                            // 清理reader引用
                            currentReader.value = null;
                        }
                    };

                    processStream();
                } else {
                    throw new Error('Invalid response format for stream mode');
                }
            } else {
                canWriteIpt.value = true;
                // 正常请求
                if (response.data.error == '1') {
                    ElMessage.error('应用不存在');
                } else if (response.data.error == '0') {
                    // 等待中消失
                    showLoading.value = false;
                    isRendering.value = false; //ai内容全部渲染完成
                    const { topic_id, content, taskmessage_id } = response.data;
                    taskmesssageList.value.push({
                        role: 'assistant',
                        content: content,
                        taskmessage_id: taskmessage_id,
                    });
                    selectedTopicId.value = topic_id;
                    // 更新用户消息的 taskmessage_id
                    userMessageObj.taskmessage_id = taskmessage_id;
                    scrollToBottom();
                    // 可以展示操作按钮
                    showOperate.value = true;
                }
            }
        }
    } catch (error) {
        debugLogger.error('发送消息失败:', error);
        showLoading.value = false;
        canWriteIpt.value = true;

        // 检查是否为用户主动中断
        if (error.name === 'AbortError' && isUserAborted.value) {
            debugLogger.log('用户主动中断，不显示错误信息');
            isUserAborted.value = false; // 重置标记
            return;
        }

        // 只有在非用户主动中断的情况下才显示错误信息
        if (!isUserAborted.value) {
            taskmesssageList.value.pop();
            taskmesssageList.value.push({
                role: 'assistant',
                content: '当前请求网络可能有问题，请重新发起对话',
            });
            scrollToBottom();
        }
        isUserAborted.value = false; // 重置标记
    }
};

// 重新生成
const againSend = async (taskmessage_id) => {
    try {
        // 使用新的 turbine-api deleteMessageApi 接口
        const response = await deleteMessageApi(
            taskmessage_id,
            selectedTopicId.value
        );

        // 新接口成功时直接处理，不需要检查 error 字段
        console.log('删除消息成功:', response);

        // 继续原有的重新生成逻辑
        // 只删除AI回复内容
        taskmesssageList.value = taskmesssageList.value.filter(
            (item) =>
                !(
                    item.taskmessage_id === taskmessage_id &&
                    item.role === 'assistant'
                )
        );

        // 获取对应的用户消息
        const userMessageObj = taskmesssageList.value.find(
            (item) =>
                item.taskmessage_id === taskmessage_id && item.role === 'user'
        );

        // 清空缓存和状态
        markdownCache.value = '';
        resetThinking(); // 重置所有思考状态

        let aiMessage = {
            role: 'assistant',
            content: '',
            taskmessage_id: '',
            thinkingContent: '', // 添加思考内容字段
            thinkingCompleted: false, // 添加 thinkingCompleted 字段
            message_comment: {},
        };
        taskmesssageList.value.push(aiMessage);
        scrollToBottom();
        showTool.value = false;
        userContent.value = ''; // 清空输入框
        showLoading.value = true;
        showOperate.value = false; //将最后一条聊天内容的操作按钮隐藏，等聊天内容全展示后，在将按钮展示
        canWriteIpt.value = false; //不能输入内容
        isRendering.value = true;
        // showPreview.value = false; //工作台隐藏预览
        clearWorkbench(); //清空工作台
        focusIpt.value = false; //失去焦点

        if (userMessageObj) {
            try {
                // 创建新的AbortController
                currentAbortController.value = new AbortController();

                const response = await sendTaskmessageApi(
                    userMessageObj.content,
                    currentApp.value.identifier || selectedAppId.value,
                    selectedTopicId.value,
                    currentApp.value.identifier ? '' : 'default',
                    currentAbortController.value
                );

                scrollToBottom();
                if (stream.value == 1) {
                    // 检查 response 是否包含流数据
                    if (response && response.getReader) {
                        const reader = response.getReader();
                        currentReader.value = reader; // 保存reader引用
                        const decoder = new TextDecoder();
                        let done = false;

                        // 处理流数据
                        const processStream = async () => {
                            try {
                                while (!done) {
                                    const { value, done: streamDone } =
                                        await reader.read();
                                    done = streamDone;
                                    if (value) {
                                        const chunk = decoder.decode(value, {
                                            stream: true,
                                        });
                                        debugLogger.log(
                                            '[Stream] 收到新的数据块'
                                        );

                                        let cacheTaskmessageId = '';

                                        const {
                                            content,
                                            taskmessage_id,
                                            error,
                                        } = parseStreamData(chunk);

                                        if (taskmessage_id) {
                                            cacheTaskmessageId = taskmessage_id;
                                        }

                                        debugLogger.log(
                                            '[Stream] 解析后的数据结构:',
                                            {
                                                content: content,
                                                hasFileList:
                                                    content.some &&
                                                    content.some(
                                                        (item) =>
                                                            item.uid ===
                                                            'file-list-component'
                                                    ),
                                                taskmessage_id: taskmessage_id,
                                                error: error,
                                            }
                                        );

                                        if (error == '1') {
                                            ElMessage.error('应用不存在');
                                            showLoading.value = false;
                                            return;
                                        }

                                        let text = '';
                                        content.map((item) => {
                                            debugLogger.log(
                                                '[Stream] 处理单条数据:',
                                                {
                                                    type: item.type,
                                                    uid: item.uid,
                                                    topic_id: item.topic_id,
                                                    area: item.area,
                                                }
                                            );

                                            if (
                                                item.type ==
                                                StreamMsgType.StartTool
                                            ) {
                                                showLoading.value = true;
                                                showTool.value = true;
                                                displayedTextLoading.value =
                                                    item.content;
                                            } else if (item.uid !== 'default') {
                                                if (!item.topic_id) {
                                                    debugLogger.log(
                                                        '[Stream] 准备处理非默认消息:',
                                                        item
                                                    );
                                                    handlePreviewMessage(item);
                                                }
                                            } else {
                                                // 处理思考中文本
                                                const messageId =
                                                    aiMessage.taskmessage_id;
                                                // debugger;
                                                // 处理文本，提取思考内容
                                                const result = processText(
                                                    item.data || '',
                                                    messageId
                                                );
                                                text += result.normalText;

                                                // 更新思考内容
                                                if (
                                                    hasThinkingContent(
                                                        messageId
                                                    )
                                                ) {
                                                    aiMessage.thinkingContent =
                                                        getThinkingContent(
                                                            messageId
                                                        );
                                                    aiMessage.thinkingCompleted =
                                                        isThinkingCompleted(
                                                            messageId
                                                        );
                                                }
                                            }
                                        });

                                        // 缓存不完整的Markdown数据
                                        markdownCache.value += text;
                                        try {
                                            // 尝试渲染Markdown
                                            aiMessage.content =
                                                markdownCache.value;
                                            if (taskmessage_id) {
                                                aiMessage.taskmessage_id =
                                                    taskmessage_id;
                                                // 更新用户消息的 taskmessage_id
                                                userMessageObj.taskmessage_id =
                                                    taskmessage_id;
                                            }
                                            // 强制 Vue 重新渲染
                                            taskmesssageList.value = [
                                                ...taskmesssageList.value,
                                            ];
                                            // console.log(taskmesssageList.value);
                                            scrollToBottom();
                                        } catch (error) {
                                            // 如果渲染失败，继续缓存
                                        }
                                    }
                                }
                                showLoading.value = false;
                                canWriteIpt.value = true;

                                inputTxtRef.value.focus();
                                scrollToBottom();
                                taskmesssageList.value = [
                                    ...taskmesssageList.value,
                                ];

                                // 流数据处理完成后，设置 showOperate 为 true
                                showOperate.value = true;
                                isRendering.value = false; //ai内容全部渲染完成
                            } catch (error) {
                                // 处理流读取过程中的错误（包括中断）
                                if (error.name === 'AbortError') {
                                    debugLogger.log('流请求被中断');
                                    // 如果是用户主动中断，不显示错误信息
                                    if (isUserAborted.value) {
                                        debugLogger.log(
                                            '用户主动中断，不显示错误信息'
                                        );
                                        isUserAborted.value = false; // 重置标记
                                        return;
                                    }
                                }
                                debugLogger.error('处理流数据失败:', error);
                                showLoading.value = false;
                                canWriteIpt.value = true;
                                // 只有在非用户主动中断的情况下才显示错误信息
                                if (!isUserAborted.value) {
                                    taskmesssageList.value.pop();
                                    taskmesssageList.value.push({
                                        role: 'assistant',
                                        content:
                                            '当前请求网络可能有问题，请重新发起对话',
                                        taskmessage_id: '',
                                        thinkingContent: '', // 添加思考内容字段
                                        thinkingCompleted: false, // 添加 thinkingCompleted 字段
                                        message_comment: {},
                                    });
                                    scrollToBottom();
                                }
                                isUserAborted.value = false; // 重置标记
                            } finally {
                                // 清理reader引用
                                currentReader.value = null;
                            }
                        };

                        processStream();
                    } else {
                        throw new Error(
                            'Invalid response format for stream mode'
                        );
                    }
                } else {
                    canWriteIpt.value = true;
                    // 正常请求
                    if (response.data.error == '1') {
                        ElMessage.error('应用不存在');
                    } else if (response.data.error == '0') {
                        // 等待中消失
                        showLoading.value = false;
                        isRendering.value = false; //ai内容全部渲染完成
                        const { topic_id, content, taskmessage_id } =
                            response.data;
                        taskmesssageList.value.push({
                            role: 'assistant',
                            content: content,
                            taskmessage_id: taskmessage_id,
                            // thinkingContent: '', // 添加思考内容字段
                            // thinkingCompleted: false, // 添加 thinkingCompleted 字段
                            // message_comment: {},
                        });
                        selectedTopicId.value = topic_id;
                        // 更新用户消息的 taskmessage_id
                        userMessageObj.taskmessage_id = taskmessage_id;
                        scrollToBottom();
                        // 可以展示操作按钮
                        showOperate.value = true;
                    }
                }
            } catch (error) {
                // 检查是否为用户主动中断
                if (error.name === 'AbortError' && isUserAborted.value) {
                    debugLogger.log('用户主动中断，不显示错误信息');
                    isUserAborted.value = false; // 重置标记
                    return;
                }

                // 只有在非用户主动中断的情况下才显示错误信息
                if (!isUserAborted.value) {
                    // ElMessage.error('网络错误');
                    aiMessage.content =
                        '当前请求网络可能有问题，请重新发起对话';
                    taskmesssageList.value = [...taskmesssageList.value];
                    showTool.value = false;
                    userContent.value = ''; // 清空输入框
                    showLoading.value = false;
                    showOperate.value = false; //将最后一条聊天内容的操作按钮隐藏，等聊天内容全展示后，在将按钮展示
                    canWriteIpt.value = false; //不能输入内容
                    isRendering.value = true;
                    // showPreview.value = false; //工作台隐藏预览
                    clearWorkbench(); //清空工作台
                    focusIpt.value = false; //失去焦点
                }
                isUserAborted.value = false; // 重置标记
            }
        }
    } catch (error) {
        console.error('删除消息失败:', error);
        ElMessage.error('删除消息失败，请重试');
    }
};

// 监听userContent的变化,如果输入框有文字，按钮背景色修改
watch(userContent, (newValue) => {
    if (newValue !== '') {
        canSendMessage.value = true;
    } else {
        canSendMessage.value = false;
    }
});

const handleWheel = (event) => {
    const containerDom = scrollContainer.value;
    if (containerDom) {
        if (event.deltaY < 0) {
            //鼠标滚动上滑
            canScrollToBottom.value = false;
        }
    }
};
// 在获取所有聊天记录时，滚动条一直置于底部
const scrollToBottom = () => {
    nextTick(() => {
        const containerDom = scrollContainer.value;
        if (containerDom) {
            containerDom.addEventListener('wheel', handleWheel);
            if (canScrollToBottom.value) {
                containerDom.scrollTop = containerDom.scrollHeight;
            }
        }
        // 用户发送消息后，输入框自动聚焦
        if (inputTxtRef.value) {
            // 重新聚焦输入框
            inputTxtRef.value.focus();
        } else {
            debugLogger.error('inputTxtRef is null');
        }
    });
};

// 监听页面滑动事件
const isCommonTopVisible = ref(true); // 控制container-queryapp-div的显示和隐藏
let lastScrollPosition = 0; // 存储上一次的滚动位置
const handleScroll = () => {
    const currentScrollPosition = scrollContainer.value.scrollTop;
    // 使用绝对值判断
    if (Math.abs(currentScrollPosition - lastScrollPosition) > 1) {
        if (lastScrollPosition == 0) {
            isCommonTopVisible.value = true;
        } else {
            if (currentScrollPosition > lastScrollPosition) {
                // 向下滚动
                isCommonTopVisible.value = false;
            } else {
                // 向上滚动
                isCommonTopVisible.value = true;
            }
        }
    }
    lastScrollPosition = currentScrollPosition; // 更新上一次的滚动位置
};

// 复制文本
const copyContent = (content) => {
    try {
        var p = /([\n\r])+/g;
        const textContent = content.replace(p, '\n'); //将文本中连续的一个或多个换行直接替换成一个换行，防止出现页面中有空行的情况

        // 创建一个临时的 textarea 元素
        const textarea = document.createElement('textarea');
        textarea.value = textContent;
        document.body.appendChild(textarea);

        // 选择并复制内容
        textarea.select();
        document.execCommand('copy');

        // 移除临时的 textarea 元素
        document.body.removeChild(textarea);

        ElMessage.success('内容已复制');
    } catch (err) {
        ElMessage.error('复制失败');
    }
};

// 获取topic的所有上下文内容
// 创建一个计算属性来获取 topic_id
const topicId = computed(() => route.query.topic_id);

// 监听计算属性
watch(topicId, (newVal) => {
    if (newVal) {
        selectedTopicId.value = newVal;
        fetchTaskmessages();
    }
});

// 监听路由参数变化，更新选中的应用ID
watch(
    () => route.params.appId,
    (newAppId) => {
        if (newAppId) {
            selectedAppId.value = newAppId;
            // 获取应用信息
            fetchAppInfo(newAppId);
        }
    },
    { immediate: true }
);
const fetchTaskmessages = async (topicId = null, skipLoading = false) => {
    // 如果应用还在加载或者明确跳过加载层，不需要额外的加载层
    if (!appLoading.value && !skipLoading) {
        appLoading.value = true;
    }
    taskmesssageList.value = [];
    const targetTopicId = topicId || route.query.topic_id;

    try {
        let response;

        // 根据配置选择不同的接口
        if (globalConfig?.apps?.isIndChat) {
            // IndChat模式使用新接口
            const params = {
                topic_id: targetTopicId,
                page: 1,
                page_size: 100, // 获取更多消息
            };
            response = await getMessagesApi(params);
        } else {
            // 普通模式使用现有接口
            response = await getTaskmessages(targetTopicId);
        }

        // 处理消息数据
        const messages = response.data.data || [];

        // 使用 for...of 循环来支持 await
        for (const item of messages) {
            // debugger;
            // 根据接口类型处理不同的数据结构
            let messageData;
            // if (globalConfig?.apps?.isIndChat) {
            //     // 新接口数据结构
            //     messageData = {
            //         id: item.id,
            //         input_content: item.input_content,
            //         output_content: item.output_content,
            //         output_attachment: item.output_attachment,
            //         create_time: item.create_time,
            //         status: item.status,
            //         // 新接口可能没有这些字段，设置默认值
            //         favorite: null,
            //         message_comment: {},
            //     };
            // } else {
            //     // 现有接口数据结构
            //     messageData = item;
            // }

            messageData = item;

            const userMessageObj = {
                role: 'user',
                content: messageData.input_content,
                taskmessage_id: messageData.id,
                create_time: messageData.create_time,
                favorite: messageData.favorite,
            };
            taskmesssageList.value.push(userMessageObj);

            const aiMessageObj = {
                role: 'assistant',
                content: '',
                taskmessage_id: messageData.id,
                create_time: messageData.create_time,
                thinkingContent: '',
                thinkingCompleted: false,
                message_comment: messageData.message_comment || {},
            };
            taskmesssageList.value.push(aiMessageObj);

            try {
                // 处理输出内容
                let outputData;
                // if (
                //     globalConfig?.apps?.isIndChat &&
                //     messageData.output_attachment
                // ) {
                //     // 新接口使用 output_attachment 字段
                //     outputData = JSON.parse(messageData.output_attachment);
                // } else {
                //     // 现有接口使用 output_content 字段
                //     outputData = JSON.parse(messageData.output_content);
                // }

                outputData = JSON.parse(messageData.output_content);
                console.log('历史记录解析数据:', outputData);

                if (outputData && Array.isArray(outputData)) {
                    // 处理所有输出项，包括嵌套的数组
                    const allItems = [];

                    // 扁平化处理：展开嵌套的数组
                    outputData.forEach((item) => {
                        if (Array.isArray(item)) {
                            // 如果是数组，展开所有子项
                            allItems.push(...item);
                        } else {
                            // 如果是对象，直接添加
                            allItems.push(item);
                        }
                    });

                    console.log('扁平化后的数据:', allItems);

                    // 使用 Promise.all 等待所有组件处理完成
                    await Promise.all(
                        allItems.map(async (outputItem) => {
                            if (outputItem.uid !== 'default') {
                                console.log(
                                    '处理历史记录组件:',
                                    outputItem.uid,
                                    outputItem.area
                                );

                                // 对于历史记录，直接处理而不使用批处理延迟
                                if (outputItem.area === 'right-bottom') {
                                    console.log(
                                        '直接处理工作台历史组件:',
                                        outputItem
                                    );

                                    // 直接调用 updatePreviewComponents 处理单个消息
                                    const updatedMessages =
                                        updatePreviewComponents([outputItem]);
                                    const msg = updatedMessages[0];

                                    if (msg && msg.component && msg.data) {
                                        // msg.component 已经被 markRaw 处理过了
                                        rightBottomComponent.value =
                                            msg.component;
                                        rightBottomComponentKey.value =
                                            msg.componentKey;
                                        rightBottomData.value = msg.data;

                                        console.log(
                                            '历史工作台组件直接更新完成:',
                                            {
                                                uid: msg.uid,
                                                componentName:
                                                    msg.component?.name ||
                                                    'Anonymous',
                                            }
                                        );
                                    }
                                } else {
                                    // 非工作台组件使用原有的批处理逻辑
                                    await new Promise((resolve) => {
                                        handlePreviewMessage(outputItem);
                                        // 给一个短暂的延时确保处理完成
                                        setTimeout(resolve, 100);
                                    });
                                }
                            } else {
                                // 处理默认文本内容
                                if (outputItem.data) {
                                    aiMessageObj.content = outputItem.data;
                                }
                            }
                        })
                    );
                } else {
                    // 如果不是数组，直接使用文本内容
                    aiMessageObj.content =
                        messageData.output_content || '暂无回答';
                }
            } catch (e) {
                // 解析失败时直接使用原始内容
                aiMessageObj.content = messageData.output_content || '暂无回答';
                console.error('解析历史消息内容失败:', e);
            }
        }
    } catch (error) {
        console.error('获取消息失败:', error);
        ElMessage.error('获取消息失败');
    } finally {
        // 只有在我们设置了加载状态时才重置它
        if (!skipLoading) {
            appLoading.value = false;
        }
    }

    // 不要在历史记录加载完成后清空工作台，因为工作台内容应该已经在处理消息时设置
    // clearWorkbench();

    canScrollToBottom.value = true; //可以执行scrollToBottom函数
    scrollToBottom();

    // 强制更新界面，确保组件能正确渲染
    nextTick(() => {
        console.log('历史记录加载完成，当前工作台状态:', {
            hasRightBottomComponent: !!rightBottomComponent.value,
            componentKey: rightBottomComponentKey.value,
            hasData: !!rightBottomData.value,
        });
    });
};

onMounted(async () => {
    console.log('IndChat onMounted 开始执行');

    // 预加载核心组件，提高首次显示速度
    try {
        preloadCoreComponents();
        console.log('核心组件预加载已启动');
    } catch (error) {
        console.warn('核心组件预加载失败:', error);
    }

    // 初始化应用信息
    if (selectedAppId.value) {
        await fetchAppInfo(selectedAppId.value);
    }

    // 如果有 title 参数，设置页面标题
    if (route.query.title) {
        try {
            const decodedTitle = decodeURIComponent(route.query.title);
            setPageTitle(decodedTitle);
        } catch (e) {
            debugLogger.error('标题解码失败:', e);
            setPageTitle(route.query.title);
        }
    }

    // 获取topic的所有上下文内容
    if (route.query.topic_id) {
        console.log('有topic_id，开始获取消息');
        fetchTaskmessages();
    }

    // 如果有 inputMsg 则直接发送消息,不展示欢迎语和推荐
    // 但需要等待应用信息加载完成
    if (inputMsg.value) {
        console.log('有inputMsg，等待应用信息加载完成后发送消息');
        userContent.value = inputMsg.value;
        // 等待应用信息加载完成后再发送
        if (selectedAppId.value && !appLoading.value) {
            sendTaskmessageHandler();
        } else {
            // 如果应用还在加载，等待加载完成
            const unwatch = watch(appLoading, (newVal) => {
                if (!newVal && selectedAppId.value) {
                    sendTaskmessageHandler();
                    unwatch(); // 取消监听
                }
            });
        }
    } else if (route.query.content) {
        console.log('有content参数');
        userContent.value = decodeURIComponent(route.query.content);
    } else {
        console.log('开始获取用户信息');
        if (!userInfo.value) {
            try {
                await userStore.fetchUserInfo();
                console.log('用户信息获取成功:', userInfo.value);
            } catch (error) {
                console.error('用户信息获取失败:', error);
            }
        }
        //1.5秒后文字逐个展示
        console.log('设置定时器显示标题');
        setTimeout(() => {
            console.log('开始显示标题1');
            showTitle1();
        }, 1000);
    }

    // 组件挂载后，获取滚动容器并添加滚动事件监听
    if (scrollContainer.value) {
        scrollContainer.value.addEventListener('scroll', handleScroll);
    }

    // 初始化消息总线
    const { onMessage } = useMessageBus();

    // 监听预览渲染消息
    onMessage(MessageType.TRIGGER_PREVIEW, handlePreviewMessageCallback);

    // 监听工作台渲染消息
    onMessage(MessageType.TRIGGER_WORKBENCH, handleWorkbenchMessageCallback);
});

onUnmounted(() => {
    // 中断当前请求
    abortCurrentRequest();

    // 组件卸载前，移除滚动事件监听
    if (scrollContainer.value) {
        scrollContainer.value.removeEventListener('scroll', handleScroll);
    }

    // 移除消息监听
    const { offMessage } = useMessageBus();
    offMessage(MessageType.TRIGGER_PREVIEW, handlePreviewMessageCallback);
    offMessage(MessageType.TRIGGER_WORKBENCH, handleWorkbenchMessageCallback);
});

// 模拟组件内容输出格式

const { updatePreviewComponents } = usePreviewComponents();

// 用于收集同一批次的预览消息
const previewMessagesBatch = ref([]);
// 批处理定时器
let batchUpdateTimer = null;
// 右侧组件内容
const rightBottomComponent = shallowRef(null);
// 右侧组件key
const rightBottomComponentKey = ref(null);
// 右侧组件数据
const rightBottomData = ref(null);

const handlePreviewMessageCallback = (message) => {
    handlePreviewMessage(message);
};

const handleWorkbenchMessageCallback = ({ uid, data }) => {
    debugLogger.log('收到工作台消息:', { uid, data });

    Promise.resolve()
        .then(() => {
            // 有新的工作台组件内容时，才清空并更新工作台
            if (uid && data) {
                try {
                    const component = resolveComponent(uid);
                    if (component) {
                        rightBottomComponent.value = markRaw(component);
                        rightBottomComponentKey.value = `${uid}-${Date.now()}`;
                        rightBottomData.value = data;

                        debugLogger.log(`工作台组件解析成功: ${uid}`);
                    } else {
                        debugLogger.error(`工作台组件解析失败: ${uid}`);
                    }
                } catch (error) {
                    debugLogger.error(`工作台组件解析出错: ${uid}`, error);
                    // 如果组件解析失败，尝试重试
                    setTimeout(() => {
                        try {
                            retryFailedComponent(uid);
                            rightBottomComponent.value = markRaw(
                                resolveComponent(uid)
                            );
                            rightBottomComponentKey.value = `${uid}-retry-${Date.now()}`;
                            rightBottomData.value = data;
                        } catch (retryError) {
                            debugLogger.error(
                                `工作台组件重试失败: ${uid}`,
                                retryError
                            );
                        }
                    }, 1000);
                }
            }

            return nextTick();
        })
        .then(() => {
            debugLogger.log('工作台状态更新:', {
                component: rightBottomComponent.value,
                rightBottomData: rightBottomData.value,
            });
        })
        .catch((error) => {
            debugLogger.error('工作台消息处理失败:', error);
        });
};

const handlePreviewMessage = (message) => {
    if (message.uid === 'file-list-component') {
        debugLogger.log('收到文件列表消息:', message);
    }

    // 特别关注工作台组件
    if (message.area === 'right-bottom') {
        console.log('收到工作台组件消息:', {
            uid: message.uid,
            area: message.area,
            data: message.data,
        });
    }

    previewMessagesBatch.value.push(message);

    if (batchUpdateTimer) {
        clearTimeout(batchUpdateTimer);
    }

    batchUpdateTimer = setTimeout(() => {
        const updatedMessages = updatePreviewComponents(
            previewMessagesBatch.value
        );

        const lastMessage =
            taskmesssageList.value[taskmesssageList.value.length - 1];
        if (lastMessage && lastMessage.role === 'assistant') {
            updatedMessages.forEach((msg) => {
                if (
                    msg.area === 'mid-txt-bottom' &&
                    msg.uid === 'file-list-component'
                ) {
                    debugLogger.log('文件列表组件更新:', {
                        组件名: msg.component,
                        组件Key: msg.componentKey,
                    });
                }

                if (msg.area === 'mid-def-top') {
                    lastMessage.topComponent = msg.component;
                    lastMessage.topComponentKey = msg.componentKey;
                    lastMessage.topData = msg.data;
                } else if (msg.area === 'mid-def-bottom') {
                    lastMessage.bottomComponent = msg.component;
                    lastMessage.bottomComponentKey = msg.componentKey;
                    lastMessage.bottomData = msg.data;
                } else if (msg.area === 'mid-txt-bottom') {
                    lastMessage.txtBottomComponent = msg.component;
                    lastMessage.txtBottomComponentKey = msg.componentKey;
                    lastMessage.txtBottomData = msg.data;
                } else if (msg.area === 'right-bottom') {
                    // 只有在有新的工作台组件内容时才更新
                    console.log('处理right-bottom组件:', {
                        uid: msg.uid,
                        hasComponent: !!msg.component,
                        hasData: !!msg.data,
                        componentKey: msg.componentKey,
                    });

                    if (msg.component && msg.data) {
                        // msg.component 已经被 markRaw 处理过了
                        rightBottomComponent.value = msg.component;
                        rightBottomComponentKey.value = msg.componentKey;
                        rightBottomData.value = msg.data;

                        console.log('工作台组件已更新:', {
                            uid: msg.uid,
                            componentName: msg.component?.name || 'Anonymous',
                            dataKeys: Object.keys(msg.data || {}),
                        });
                    } else {
                        console.warn('工作台组件数据不完整:', msg);
                    }
                }
            });

            taskmesssageList.value = [...taskmesssageList.value];

            nextTick(() => {
                if (lastMessage.txtBottomComponent === 'file-list-component') {
                    debugLogger.log('DOM更新后文件列表组件状态:', {
                        是否存在组件: !!lastMessage.txtBottomComponent,
                        组件数据: lastMessage.txtBottomData,
                    });
                }

                setTimeout(() => {
                    if (canScrollToBottom.value) {
                        scrollToBottom();
                    }
                }, 100);
            });
        }

        previewMessagesBatch.value = [];
        batchUpdateTimer = null;
    }, 0);
};

// 点赞，点踩操作
const sendCommentVote = debounce(async (message, vote) => {
    console.log(message, '----点赞');
    try {
        const response = await addMessageCommentApi(
            message.taskmessage_id,
            vote
        );
        if (response.data.error == '0') {
            message.message_comment.vote = vote;
        } else {
            ElMessage.error(response.data.message);
        }
    } catch (e) {
        ElMessage.error('操作错误', e);
    }
}, 100);

// 点踩
const delCommentVote = debounce(async (message) => {
    // console.log(message, '----点踩');

    try {
        const response = await delMessageCommentApi(message.taskmessage_id);
        // console.log(response);
        if (response.data.error == '0') {
            message.message_comment.vote = '';
        } else {
            ElMessage.error(response.data.message);
        }
    } catch (e) {
        ElMessage.error('操作错误', e);
    }
}, 100);

// ChatHeader事件处理方法
const handleNewChat = () => {
    // 先中断当前请求（用户主动操作）
    abortCurrentRequest(true);

    // 清空对话列表
    taskmesssageList.value = [];

    // 重置相关状态
    selectedTopicId.value = null;
    userContent.value = '';
    showLoading.value = false;
    showOperate.value = true;
    canWriteIpt.value = true;
    isRendering.value = false;
    focusIpt.value = false;

    // 清空工作台
    clearWorkbench();

    // 重置思考状态
    resetThinking();

    // 清空markdown缓存和JSON缓存
    markdownCache.value = '';
    jsonCache.value = '';

    // 滚动到顶部
    nextTick(() => {
        if (scrollContainer.value) {
            scrollContainer.value.scrollTop = 0;
        }
    });

    ElMessage.success('已开启新对话');
};

const handleHistory = () => {
    showHistoryPanel.value = true;
};

// 关闭历史对话面板
const handleCloseHistory = () => {
    showHistoryPanel.value = false;
};

// 选择历史对话
const handleSelectHistory = async (topicId, skipLoading = false) => {
    // 先中断当前请求（用户主动操作）
    abortCurrentRequest(true);

    // 清空当前对话列表
    taskmesssageList.value = [];

    // 重置相关状态
    selectedTopicId.value = topicId;
    userContent.value = '';
    showLoading.value = false;
    showOperate.value = true;
    canWriteIpt.value = true;
    isRendering.value = false;
    focusIpt.value = false;

    // 清空工作台
    clearWorkbench();

    // 重置思考状态
    resetThinking();

    // 清空markdown缓存和JSON缓存
    markdownCache.value = '';
    jsonCache.value = '';

    // 加载历史消息
    await fetchTaskmessages(topicId, skipLoading);

    // 滚动到顶部
    nextTick(() => {
        if (scrollContainer.value) {
            scrollContainer.value.scrollTop = 0;
        }
    });
};

// 处理建议问题点击
const handleSuggestedQuestion = (question) => {
    // 将建议问题填入输入框
    userContent.value = question;
    // 自动发送消息
    nextTick(() => {
        sendTaskmessageHandler();
    });
};

// 收藏指令
const handleFavorite = debounce(async (message, vote) => {
    // 设置加载状态
    message.favoriteLoading = true;

    try {
        if (vote == '0') {
            const response = await delFavoriteApi(message.favorite.id);
            if (response.data.error == '0') {
                message.favorite = null;
            } else {
                ElMessage.error(response.data.message);
            }
        } else {
            const response = await addFavoriteApi(
                message.taskmessage_id,
                message.content,
                route.query.app_id || ''
            );
            if (response.data.error == '0') {
                message.favorite = {
                    id: response.data.favorite_id,
                };
            } else {
                ElMessage.error(response.data.message);
            }
        }
    } catch (e) {
        ElMessage.error('操作错误', e);
    } finally {
        // 无论成功失败都取消加载状态
        message.favoriteLoading = false;
    }
}, 100);

const isInputFocused = ref(false);
// onMounted(() => {
//   document.addEventListener('paste', handlePaste);
// });

// onUnmounted(() => {
//   document.removeEventListener('paste', handlePaste);
// });

const handlePaste = (e) => {
    if (isInputFocused.value) {
        console.log('输入框聚焦，允许默认粘贴行为');
        return; // 允许默认粘贴行为
    }

    console.log('输入框未聚焦，执行子组件的文件粘贴逻辑');
    e.preventDefault(); // 阻止默认粘贴行为
};

const handleInputFocus = () => {
    isInputFocused.value = true;
    focusIpt.value = true;
    // console.log('输入框聚焦');
};

const handleInputBlur = () => {
    isInputFocused.value = false;
    // console.log('输入框失焦');
};
</script>
<style scoped>
@import url('@/assets/styles/chat/index.css');
.taskmessage-add > div {
    padding: 15px 20px;
    min-height: 116px;
}
:deep(.el-textarea__inner) {
    padding: 0;
}

/* 解决 markdown 渲染后底部多余高度问题 */
:deep(.markdown-content) {
    display: block;
}

:deep(.markdown-content > *:last-child) {
    margin-bottom: 0;
    padding-bottom: 0;
}

:deep(.markdown-content p) {
    margin: 0;
    padding: 0;
}

:deep(.markdown-content p:last-child) {
    margin: 0;
    padding: 0;
    line-height: 1;
}

/* 用户消息特定处理 */
:deep(.user-message) {
    line-height: 30px;
    display: inline-block;
}

:deep(.user-message p) {
    display: inline;
    margin: 0;
    padding: 0;
    line-height: inherit;
}

/* 普通模式下的用户消息样式 */
.user-message-bubble {
    position: relative;
    border-radius: 16px;
    background: rgba(18, 155, 255, 0.1);
    color: #333;
    font-size: 14px;
    line-height: 22px;
    padding: 12px 16px;
    word-wrap: break-word;
    word-break: break-all;
}

/* .user-message-bubble::after {
    content: '';
    position: absolute;
    top: 12px;
    right: -8px;
    width: 0;
    height: 0;
    border-left: 8px solid rgba(18, 155, 255, 0.1);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
} */

/* 确保普通模式下的markdown内容样式正确 */
.user-message-bubble :deep(.markdown-content) {
    color: #333 !important;
}

.user-message-bubble :deep(.markdown-content p) {
    margin: 0;
    color: #333 !important;
}

.user-message-bubble :deep(.markdown-content code) {
    background-color: rgba(0, 0, 0, 0.1);
    color: #333;
    padding: 2px 4px;
    border-radius: 4px;
}

.user-message-bubble :deep(.markdown-content pre) {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    padding: 12px;
    margin: 8px 0;
}

.user-message-bubble :deep(.markdown-content pre code) {
    background-color: transparent;
    padding: 0;
    color: #333;
}

/* AI消息气泡样式 */
.ai-message-bubble {
    position: relative;
    top: -10px;
    border-radius: 16px;
    border: 1px solid #cfeafe;
    background: #ffffff;
    color: #333;
    font-size: 14px;
    line-height: 22px;
    padding: 16px;
    word-wrap: break-word;
    word-break: break-all;
    margin-top: 8px;
}

/* AI消息气泡内的markdown内容样式 */
.ai-message-bubble :deep(.markdown-content) {
    color: #333 !important;
}

.ai-message-bubble :deep(.markdown-content p) {
    margin: 0;
    color: #333 !important;
}

.ai-message-bubble :deep(.markdown-content code) {
    background-color: rgba(18, 155, 255, 0.1);
    color: #333;
    padding: 2px 4px;
    border-radius: 4px;
}

.ai-message-bubble :deep(.markdown-content pre) {
    background-color: rgba(18, 155, 255, 0.05);
    border-radius: 6px;
    padding: 12px;
    margin: 8px 0;
}

.ai-message-bubble :deep(.markdown-content pre code) {
    background-color: transparent;
    padding: 0;
    color: #333;
}

.wrapMessageContent :deep(img) {
    max-width: 100%;
}
</style>
