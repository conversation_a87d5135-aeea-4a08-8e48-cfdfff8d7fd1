<!-- 数据包编辑组件 -->
<template>
  <div class="mx-auto text-[#343A3F] text-sm h-full flex flex-col justify-between w-full">
    <div class="container-center-div overflow-y-auto overflow-scrollbar-hidden flex flex-col">
      <!-- 数据包基本信息 -->
      <div class="pt-3 pb-2 border-0 border-solid border-b border-[#e5e5e5] flex-shrink-0">
        <div class="w-full flex flex-wrap">
          <div class="flex items-center mr-5 mb-4 flex-shrink-0">
            <span class="flex-shrink-0 mr-1">机组型号<span class="text-red-500">*</span></span>
            <div class="relative flex items-center">
              <el-input v-model="dataPackage.model_number" disabled class="!max-w-[142px]" />
              <el-button
                type=""
                size="small"
                text
                class="w-[22px] h-[22px] ml-1 flex-shrink-0"
                @click="showEditData('model_number')"
              >
                <img src="@/assets/images/qlj/edit.png" alt="" class="w-[22px]" />
              </el-button>
              <div
                v-if="selectedEditKey == 'model_number'"
                class="absolute top-0 left-0 w-full h-full flex items-center justify-between bg-[#F7F9FC] shadow-sm border border-solid border-[#129BFE] rounded-lg p-2"
              >
                <el-input v-model="tempEditValues.model_number" size="small" maxlength="100" />
                <div class="flex-shrink-0 flex items-center">
                  <el-button
                    type=""
                    size="small"
                    text
                    class="!w-3 !h-3 !ml-1 !p-0"
                    @click="handleEditTitle()"
                  >
                    <img src="@/assets/images/qlj/check.png" class="w-4 h-4" />
                  </el-button>
                  <el-button
                    type=""
                    size="small"
                    text
                    class="!w-3 !h-3 !ml-1 !p-0"
                    @click="cancelEditData()"
                  >
                    <img src="@/assets/images/qlj/close.png" class="w-4 h-4" />
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center mr-5 mb-4 flex-shrink-0">
            <span class="flex-shrink-0 mr-1">气缸编号<span class="text-red-500">*</span></span>
            <div class="relative flex items-center">
              <el-input v-model="dataPackage.cylinder_number" disabled class="!w-[50px]" />
              <el-button
                type=""
                size="small"
                text
                class="w-[22px] h-[22px] ml-1 flex-shrink-0"
                @click="showEditData('cylinder_number')"
              >
                <img src="@/assets/images/qlj/edit.png" alt="" class="w-[22px]" />
              </el-button>
              <div
                v-if="selectedEditKey == 'cylinder_number'"
                class="absolute top-0 left-0 w-full h-full flex items-center justify-between bg-[#F7F9FC] shadow-sm border border-solid border-[#129BFE] rounded-lg p-2"
              >
                <el-input v-model="tempEditValues.cylinder_number" size="small" maxlength="100" />
                <div class="flex-shrink-0 flex items-center">
                  <el-button
                    type=""
                    size="small"
                    text
                    class="!w-3 !h-3 !ml-1 !p-0"
                    @click="handleEditTitle()"
                  >
                    <img src="@/assets/images/qlj/check.png" class="w-4 h-4" />
                  </el-button>
                  <el-button
                    type=""
                    size="small"
                    text
                    class="!w-3 !h-3 !ml-1 !p-0"
                    @click="cancelEditData()"
                  >
                    <img src="@/assets/images/qlj/close.png" class="w-4 h-4" />
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center mr-5 mb-4 flex-shrink-0">
            <span class="flex-shrink-0 mr-1">气缸名称<span class="text-red-500">*</span></span>
            <div class="relative flex items-center">
              <el-input v-model="dataPackage.cylinder_name" disabled class="!max-w-[142px]" />
              <el-button
                type=""
                size="small"
                text
                class="w-[22px] h-[22px] ml-1 flex-shrink-0"
                @click="showEditData('cylinder_name')"
              >
                <img src="@/assets/images/qlj/edit.png" alt="" class="w-[22px]" />
              </el-button>
              <div
                v-if="selectedEditKey == 'cylinder_name'"
                class="absolute top-0 left-0 w-full h-full flex items-center justify-between bg-[#F7F9FC] shadow-sm border border-solid border-[#129BFE] rounded-lg p-2"
              >
                <el-input v-model="tempEditValues.cylinder_name" size="small" maxlength="100" />
                <div class="flex-shrink-0 flex items-center">
                  <el-button
                    type=""
                    size="small"
                    text
                    class="!w-3 !h-3 !ml-1 !p-0"
                    @click="handleEditTitle()"
                  >
                    <img src="@/assets/images/qlj/check.png" class="w-4 h-4" />
                  </el-button>
                  <el-button
                    type=""
                    size="small"
                    text
                    class="!w-3 !h-3 !ml-1 !p-0"
                    @click="cancelEditData()"
                  >
                    <img src="@/assets/images/qlj/close.png" class="w-4 h-4" />
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center mb-4 flex-shrink-0">
            <span class="flex-shrink-0 mr-1">起始级号<span class="text-red-500">*</span></span>
            <div class="relative flex items-center">
              <el-input v-model="dataPackage.start_stage" disabled class="!w-[50px]" />
              <el-button
                type=""
                size="small"
                text
                class="w-[22px] h-[22px] ml-1 flex-shrink-0"
                @click="showEditData('start_stage')"
              >
                <img src="@/assets/images/qlj/edit.png" alt="" class="w-[22px]" />
              </el-button>
              <div
                v-if="selectedEditKey == 'start_stage'"
                class="absolute top-0 left-0 w-full h-full flex items-center justify-between bg-[#F7F9FC] shadow-sm border border-solid border-[#129BFE] rounded-lg p-2"
              >
                <el-input v-model="tempEditValues.start_stage" size="small" maxlength="100" />
                <div class="flex-shrink-0 flex items-center">
                  <el-button
                    type=""
                    size="small"
                    text
                    class="!w-3 !h-3 !ml-1 !p-0"
                    @click="handleEditTitle()"
                  >
                    <img src="@/assets/images/qlj/check.png" class="w-4 h-4" />
                  </el-button>
                  <el-button
                    type=""
                    size="small"
                    text
                    class="!w-3 !h-3 !ml-1 !p-0"
                    @click="cancelEditData()"
                  >
                    <img src="@/assets/images/qlj/close.png" class="w-4 h-4" />
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-between items-center">
          <div class="text-[15px] text-[#121619] w-[80%] truncate">
            数据包 {{ dataPackage.number }}
          </div>
          <el-button
            class="!text-xs flex items-center !pl-2 !pr-[10px] !h-[22px] rounded-md !bg-[#CFEAFE] !text-[#129BFE] !border-0 ml-2"
            @click="handleDownloadDataPackage(dataPackage?.id, dataPackage.number)"
          >
            <img src="@/assets/images/qlj/download.png" alt="" class="w-4" />
            下载
          </el-button>
        </div>
      </div>
      <!-- 数据包叶片信息 -->
      <div class="flex-1 flex flex-col">
        <div class="p-3 flex flex-col flex-1">
          <template v-if="dataPackage.blade_sets.length > 0">
            <!-- 部套tab切换 -->
            <ChangeBlade
              v-model="selectedBlade"
              :blade-sets="dataPackage.blade_sets"
              class="flex-shrink-0"
            />
            <!-- 叶片信息 -->
            <BladeStageDetail
              :blades-stage-list="bladesStageList"
              @update="handleBladeStageUpdate"
              :editable="true"
              :stage-index="selectedStageIndex"
              :selected-blade-data-nav="selectedBladeDataNav"
              :selected-compose-nav="selectedComposeNav"
              @handle-stage-change="handleStageChange"
              @handle-compose-nav-change="handleComposeNavChange"
              @handle-blade-data-nav-change="handleBladeDataNavChange"
              :key="reloadKey"
            />
          </template>
        </div>
      </div>
    </div>
    <!-- 上传 -->
    <div
      class="flex justify-between py-4 border-0 border-solid border-t border-[#e5e5e5] flex-shrink-0"
    >
      <el-button class="flex-1 flex-shrink-0 !h-10 !rounded-md" @click="handleCancelSubmit()"
        >取消上传</el-button
      >
      <el-button
        type="primary"
        class="flex-1 flex-shrink-0 ml-4 !h-10 !rounded-md"
        @click="handleSubmitToTc()"
        >确认上传到TC</el-button
      >
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, nextTick } from 'vue';
import { ElIcon, ElLoading, ElMessage } from 'element-plus';
// import { Check, Close } from '@element-plus/icons-vue';
import ChangeBlade from './ChangeBlade.vue';
import BladeStageDetail from './BladeStageDetail.vue';
import {
  fetchUpdateDataPackage,
  fetchDownloadDataPackage,
  updateBladesProcessesApi,
} from '@/services/turbine-api';
import { debounce } from '@/utils/utils';

const props = defineProps({
  dataPackage: {
    type: Object,
    required: true,
  },
  useMockData: {
    type: Boolean,
    default: true,
  },
  isReload: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update', 'cancel', 'submit']);

const selectedEditKey = ref(''); //选中的编辑字段key
const tempEditValues = ref({}); // 存储所有字段的临时编辑值
const selectedBlade = ref(''); // 选中的部套类型
const bladesData = ref({}); //指定type的部套信息
const bladesStageList = ref([]); // 叶片级号列表
const selectedStageIndex = ref(0); // 设置叶片级号下标初始值为0
const selectedBladeDataNav = ref('basic');
const selectedComposeNav = ref('shroud');
const elLoading = ref(null);
const reloadKey = ref(0);
// 是否调用接口获取datapackage
const isCallApi = ref(false);

onMounted(() => {
  selectedBlade.value = props.dataPackage.blade_sets[0].type;
});

// 重置selectedStageIndex，selectedStageIndex，selectedComposeNav为初始值
const resetSelectedBlade = () => {
  selectedStageIndex.value = 0;
  selectedBladeDataNav.value = 'basic';
  selectedComposeNav.value = 'shroud';
};

// 监听 isReload 的变化
watch(
  () => props.isReload,
  newValue => {
    if (newValue) {
      // selectedStageIndex.value = 0;
      // selectedBladeDataNav.value = 'basic';
      // selectedComposeNav.value = 'shroud';
      resetSelectedBlade();
    }
  },
  { immediate: true }
);
// 监听子组件选中叶片级号变化，修改父组件中的级号
const handleStageChange = index => {
  selectedStageIndex.value = index;
};

// 监听 selectedComposeNav 的变化
const handleComposeNavChange = nav => {
  selectedComposeNav.value = nav;
};

// 监听 selectedBladeDataNav 的变化
const handleBladeDataNavChange = nav => {
  selectedBladeDataNav.value = nav;
};

// 监听 dataPackage 的变化
watch(
  () => props.dataPackage,
  newValue => {
    if (isCallApi.value) {
      if (newValue && newValue.blade_sets && newValue.blade_sets.length > 0) {
        // 重新设置 selectedBlade 的值，触发 watch
        resetSelectedBlade();
        // 增加 reloadKey 的值，强制 BladeStageDetail 重新渲染
        reloadKey.value++;
        bladesData.value = props.dataPackage.blade_sets.find(
          item => item.type === selectedBlade.value
        );
        bladesStageList.value = bladesData.value?.blades || [];
      }
    }
  },
  { deep: true }
);

// 监听 selectedBlade 的变化
watch(
  selectedBlade,
  newValue => {
    bladesData.value = props.dataPackage.blade_sets.find(item => item.type === newValue);
    bladesStageList.value = bladesData.value?.blades || [];
    // 直接设置值，不使用 nextTick
    // selectedStageIndex.value = 0;
    resetSelectedBlade();
  },
  { immediate: true }
);

// 修改字段信息
const showEditData = _selectedEditKey => {
  selectedEditKey.value = _selectedEditKey;
  tempEditValues.value[_selectedEditKey] = props.dataPackage[_selectedEditKey]; // 初始化临时值为当前值
};

// 保存编辑信息
const handleEditTitle = async () => {
  if (selectedEditKey.value) {
    try {
      elLoading.value = ElLoading.service({
        lock: true,
        text: '加载中',
        background: 'rgba(0, 0, 0, 0.5)',
      });
      if (props.useMockData) {
        const updatedDataPackage = { ...props.dataPackage };
        updatedDataPackage[selectedEditKey.value] = tempEditValues.value[selectedEditKey.value];
        emit('update', updatedDataPackage);
      } else {
        // 调用接口,重新获取数据包
        const res = await fetchUpdateDataPackage(props.dataPackage.id, {
          key: selectedEditKey.value,
          value: tempEditValues.value[selectedEditKey.value],
        });
        elLoading.value.close();
        if (res.data.code == '0') {
          // console.log('传递给父组件的数据', res.data.data);
          isCallApi.value = true;
          emit('update', res.data.data);
          console.log('传递成功');
        } else if (res.data.msg) {
          ElMessage.error(res.data.msg);
        } else {
          ElMessage.error('修改失败');
        }
      }
    } catch (error) {
      console.log('修改失败', error);
      ElMessage.error('修改失败');
    } finally {
      elLoading.value.close();
      selectedEditKey.value = '';
    }
  }
  selectedEditKey.value = '';
};

// 取消编辑
const cancelEditData = () => {
  selectedEditKey.value = '';
};

// // 监听部套切换
// const handleBladeSetUpdate = type => {
//   console.log(type, '切换');
//   // selectedBlade.value = type;
// };

// 处理叶片信息更新
const handleBladeStageUpdate = async ({ type, stage, key, value, bdid }) => {
  console.log('更新叶片信息:', { type, stage, key, value, bdid });
  isCallApi.value = false;
  // const updatedDataPackage = { ...props.dataPackage };
  // const bladeSet = updatedDataPackage.blade_sets.find(item => item.type === type);
  // if (bladeSet) {
  //   const blade = bladeSet.blades.find(item => item.stage === stage);
  //   if (blade && blade.blade_data) {
  //     const basicItem = blade.blade_data.basic.find(item => item.key === key);

  //     if (basicItem) {
  //       basicItem.value = value;
  //       if (key == 'BLADES1') {
  //         // 如果修改的是加厚叶片只数，那么就要相应修改标准叶片只数，公式为：标准叶片=整级叶片-加厚叶片-末叶片
  //         const bladesItem = blade.blade_data.basic.find(item => item.key === 'BLADES');
  //         const blades2Item = blade.blade_data.basic.find(item => item.key === 'BLADES2');
  //         const blades0Item = blade.blade_data.basic.find(item => item.key === 'BLADES0');
  //         if (bladesItem && blades2Item && basicItem && blades0Item) {
  //           const blades = parseFloat(bladesItem.value) || 0;
  //           const blades2 = parseFloat(blades2Item.value) || 0;
  //           const blades1 = parseFloat(value) || 0;

  //           // Calculate BLADES0 = BLADES - BLADES2 - BLADES1
  //           const blades0 = blades - blades2 - blades1;
  //           blades0Item.value = blades0.toString();
  //         }
  //       }
  //     }
  //   }
  // }

  try {
    elLoading.value = ElLoading.service({
      lock: true,
      text: '加载中',
      background: 'rgba(0, 0, 0, 0.5)',
    });
    if (props.useMockData) {
      const updatedDataPackage = { ...props.dataPackage };
      const bladeSet = updatedDataPackage.blade_sets.find(item => item.type === type);
      if (bladeSet) {
        const blade = bladeSet.blades.find(item => item.stage === stage);
        if (blade && blade.blade_data) {
          const basicItem = blade.blade_data.basic.find(item => item.key === key);
          if (basicItem) {
            basicItem.value = value;
            if (key == 'BLADES1') {
              // 如果修改的是加厚叶片只数，那么就要相应修改标准叶片只数，公式为：标准叶片=整级叶片-加厚叶片-末叶片
              const bladesItem = blade.blade_data.basic.find(item => item.key === 'BLADES');
              const blades2Item = blade.blade_data.basic.find(item => item.key === 'BLADES2');
              const blades0Item = blade.blade_data.basic.find(item => item.key === 'BLADES0');
              if (bladesItem && blades2Item && basicItem && blades0Item) {
                const blades = parseFloat(bladesItem.value) || 0;
                const blades2 = parseFloat(blades2Item.value) || 0;
                const blades1 = parseFloat(value) || 0;

                // Calculate BLADES0 = BLADES - BLADES2 - BLADES1
                const blades0 = blades - blades2 - blades1;
                blades0Item.value = blades0.toString();
              }
            }
          }
        }
      }
    } else {
      // 调用接口,修改叶片信息
      const res = await updateBladesProcessesApi(bdid, { key: key, value: value });
      elLoading.value.close();
      if (res.data.code == '0') {
        const updatedDataPackage = { ...props.dataPackage };
        const bladeSet = updatedDataPackage.blade_sets.find(item => item.type === type);
        if (bladeSet) {
          const blade = bladeSet.blades.find(item => item.stage === stage);
          if (blade && blade.blade_data) {
            const basicItem = blade.blade_data.basic.find(item => item.key === key);
            if (basicItem) {
              basicItem.value = value;
              if (key == 'BLADES1') {
                // 如果修改的是加厚叶片只数，那么就要相应修改标准叶片只数，公式为：标准叶片=整级叶片-加厚叶片-末叶片
                const bladesItem = blade.blade_data.basic.find(item => item.key === 'BLADES');
                const blades2Item = blade.blade_data.basic.find(item => item.key === 'BLADES2');
                const blades0Item = blade.blade_data.basic.find(item => item.key === 'BLADES0');
                if (bladesItem && blades2Item && basicItem && blades0Item) {
                  const blades = parseFloat(bladesItem.value) || 0;
                  const blades2 = parseFloat(blades2Item.value) || 0;
                  const blades1 = parseFloat(value) || 0;

                  // Calculate BLADES0 = BLADES - BLADES2 - BLADES1
                  const blades0 = blades - blades2 - blades1;
                  blades0Item.value = blades0.toString();
                }
              }
            }
          }
        }
      } else if (res.data.msg) {
        // ElMessage.error(res.data.msg);
        ElMessage({
          message: res.data.msg,
          type: 'error',
          duration: 2000, // 消息提示框显示2秒
          onClose: () => {
            location.reload(); // 消息提示框关闭后刷新页面
          },
        });
      } else {
        // ElMessage.error('修改失败');
        ElMessage({
          message: '修改失败',
          type: 'error',
          duration: 2000, // 消息提示框显示2秒
          onClose: () => {
            location.reload(); // 消息提示框关闭后刷新页面
          },
        });
      }
    }
  } catch (error) {
    console.log('修改失败', error);
    ElMessage({
      message: '修改失败',
      type: 'error',
      duration: 2000, // 消息提示框显示2秒
      onClose: () => {
        location.reload(); // 消息提示框关闭后刷新页面
      },
    });
  } finally {
    elLoading.value.close();
  }
  // console.log(updatedDataPackage, 'updatedDataPackage');
  // emit('update:dataPackage', updatedDataPackage);
};

// 提交数据到tc
const handleSubmitToTc = debounce(() => {
  emit('submit', props.dataPackage);
});

// 取消上传
const handleCancelSubmit = debounce(() => {
  emit('cancel', props.dataPackage);
});

// 下载数据包 文件流格式
const handleDownloadDataPackage = async (id, number) => {
  try {
    elLoading.value = ElLoading.service({
      lock: true,
      text: '加载中',
      background: 'rgba(0, 0, 0, 0.5)',
    });

    // 调用fetchDownloadDataPackage获取文件流
    const blob = await fetchDownloadDataPackage(id);

    // 创建一个指向Blob对象的URL
    const downloadUrl = window.URL.createObjectURL(blob);

    // 创建临时a标签并触发下载
    const link = document.createElement('a');

    link.href = downloadUrl;
    if (number) {
      link.download = '数据包' + number + '.zip'; // 使用服务器返回的文件名
    } else {
      link.download = '数据包.zip'; // 使用服务器返回的文件名
    }
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('下载失败:', error);
    ElMessage.error('下载失败，请重试');
  } finally {
    elLoading.value.close();
  }
};
</script>

<style scoped>
.container-center-div {
  height: calc(100% - 40px);
}
:deep(.el-input__wrapper:hover),
:deep(.el-input.is-disabled .el-input__wrapper) {
  box-shadow: none;
  background: #f7f9fc;
}
:deep(.el-input.is-disabled .el-input__wrapper),
:deep(.el-input.is-disabled .el-input__inner) {
  cursor: default !important;
  color: #343a3f !important;
  -webkit-text-fill-color: #343a3f !important;
}
:deep(.el-input__wrapper) {
  background: transparent;
  box-shadow: none;
  border-radius: 8px;
  overflow: hidden;
  display: inline-block;
}
:deep(.el-tabs__item.is-active) {
  color: #129bfe;
}
:deep(.el-tabs__nav-wrap::after) {
  display: none;
}
:deep(.el-tabs__nav) {
  border: none;
}
:deep(.el-tabs__active-bar) {
  background-color: #129bfe;
}
:deep(.el-tabs__item) {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: auto;
  padding: 0 20px;
}
:deep(.el-tabs__header) {
  margin: 0;
}
</style>
