<template>
    <div class="flex flex-col h-screen v3-gradient-bg relative">
        <Header :showWorkspaceButton="false" :showBackHomeButton="true" />

        <div class="flex flex-1 overflow-hidden">
            <!-- 左侧应用列表 -->
            <aside class="w-[240px] bg-white h-full flex flex-col">
                <!-- 应用列表 -->
                <nav
                    class="flex-1 overflow-y-auto pt-[20px] pb-[20px] px-[16px]"
                >
                    <div class="space-y-[12px]">
                        <!-- 应用列表标题 -->
                        <!-- <div
                            class="text-[12px] leading-4 font-medium text-[#343A3F] py-3 mt-2"
                        >
                            {{ $t('indchat.appList') }}
                        </div> -->

                        <!-- 加载状态 -->
                        <div
                            v-if="loading"
                            class="flex justify-center items-center py-4 text-gray-500"
                        >
                            <el-icon class="is-loading"><Loading /></el-icon>
                            <span class="ml-2 text-sm"
                                >{{ $t('common.loading') }}...</span
                            >
                        </div>

                        <!-- 应用列表项 -->
                        <template v-for="app in appList" :key="app.id">
                            <div
                                :class="[
                                    'flex items-center h-[42px] px-3 py-2 text-[14px] leading-[22px] font-medium transition-colors duration-200 rounded-md cursor-pointer',
                                    currentAppId === app.id
                                        ? 'bg-[#EBF7FF] text-[#343A3F]'
                                        : 'text-[#343A3F] hover:bg-[#F5F5F5]',
                                ]"
                                @click="handleAppClick(app)"
                            >
                                <!-- 应用图标 -->
                                <div class="relative mr-3 flex-shrink-0">
                                    <div
                                        v-if="app.icon_type === 'emoji'"
                                        class="w-[24px] h-[24px] rounded-full flex items-center justify-center text-white text-sm font-medium"
                                        :style="`background-color: ${
                                            app.icon_background || '#FFEAD5'
                                        }`"
                                    >
                                        <EmojiIcon
                                            :emojiId="app.icon"
                                            :size="14"
                                        />
                                    </div>
                                    <div
                                        v-else-if="app.icon_url"
                                        class="w-[36px] h-[36px] rounded-full flex items-center justify-center"
                                    >
                                        <img
                                            :src="getIconUrl(app.icon_url)"
                                            :alt="app.name"
                                            class="w-[36px] h-[36px] object-contain rounded-full"
                                        />
                                    </div>
                                    <div
                                        v-else
                                        class="w-[24px] h-[24px] rounded-full flex items-center justify-center text-white text-sm font-medium"
                                        :style="`background-color: ${
                                            app.icon_background || '#FFEAD5'
                                        }`"
                                    >
                                        {{ app.name?.charAt(0) }}
                                    </div>
                                </div>

                                <!-- 应用名称 -->
                                <span class="truncate flex-1">{{
                                    app.name
                                }}</span>
                            </div>
                        </template>

                        <!-- 空状态 -->
                        <div
                            v-if="!loading && appList.length === 0"
                            class="flex flex-col items-center justify-center py-8 text-gray-500"
                        >
                            <div class="text-sm">
                                {{ $t('indchat.noApps') }}
                            </div>
                        </div>
                    </div>
                </nav>
            </aside>

            <!-- 右侧内容区 -->
            <div
                class="flex-1 border-0 border-solid border-l-[1px] border-[#E5E5E5] overflow-auto bg-transparent relative"
            >
                <router-view v-slot="{ Component }">
                    <component :is="Component" :key="route.fullPath" />
                </router-view>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { Loading } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import Header from './Header.vue';
import EmojiIcon from '@/components/EmojiIcon/index.vue';
import { fetchAppListApi } from '@/services/console-api';
import { getAllAppsApi } from '@/services/turbine-api';
import { transformAppListResponse } from '@/utils/appDataTransform';
import { getConsoleApiUrl } from '@/utils/apiUrl';
import { useConfig } from '@/hooks/useConfig';

// 初始化i18n
const { t } = useI18n();

// 初始化配置
const { globalConfig } = useConfig();

const route = useRoute();
const router = useRouter();

// 应用列表相关
const appList = ref([]);
const loading = ref(false);

// IndChat模式下的原始数据缓存
const originalAppList = ref([]);

// 当前选中的应用ID，从路由参数获取
const currentAppId = computed(() => route.params.appId as string);

// 处理图标URL，如果是相对路径则拼接上API基础URL
const getIconUrl = (url: string) => {
    // 如果是完整的URL（以http或https开头），直接返回
    if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
        return url;
    }
    // 否则使用原有逻辑拼接API基础URL
    return getConsoleApiUrl(url);
};

// 加载应用列表数据
const loadAppList = async (forceRefresh = false) => {
    if (loading.value) return;
    loading.value = true;

    try {
        let response: any;

        // 根据配置选择不同的接口
        if (globalConfig?.apps?.isIndChat) {
            // IndChat模式：如果已有缓存数据且不是强制刷新，直接使用缓存
            if (originalAppList.value.length > 0 && !forceRefresh) {
                appList.value = originalAppList.value;
                loading.value = false;
                return;
            }

            // IndChat模式使用新接口
            response = await getAllAppsApi();
        } else {
            // 普通模式使用现有接口
            const params = {
                page: 1,
                limit: 100, // 获取所有应用
                mode: 'chat', // 只获取聊天类型的应用
            };
            response = await fetchAppListApi(params);
        }

        // 使用转换工具统一处理数据格式
        const { apps: transformedApps } = transformAppListResponse(
            response,
            globalConfig?.apps?.isIndChat
        );

        appList.value = transformedApps;

        // IndChat模式下缓存原始数据
        if (globalConfig?.apps?.isIndChat) {
            originalAppList.value = transformedApps;
        }
    } catch (error) {
        console.error('加载应用列表失败:', error);
        ElMessage.error(t('apps.loadFailed'));
    } finally {
        loading.value = false;
    }
};

// 处理应用点击
const handleAppClick = (app: any) => {
    // 强制刷新页面，无论是否在当前应用下
    const targetUrl = `/indchat/${app.id}`;

    // 如果点击的是当前应用，使用 window.location 强制刷新页面
    if (currentAppId.value === app.id) {
        window.location.href = '/nq' + targetUrl;
    } else {
        // 跳转到对应应用的 indchat 页面
        router.push(targetUrl);
    }
};

// 刷新应用列表
const refreshAppList = () => {
    loadAppList(true);
};

// 在组件挂载时加载应用列表
onMounted(() => {
    loadAppList();
});

// 如果没有应用ID参数且有应用列表，自动跳转到第一个应用
const handleAutoRedirect = () => {
    if (!currentAppId.value && appList.value.length > 0) {
        router.replace(`/indchat/${appList.value[0].id}`);
    }
};

// 监听应用列表变化，处理自动跳转
const unwatchAppList = computed(() => {
    if (appList.value.length > 0) {
        handleAutoRedirect();
    }
    return appList.value;
});

// 确保监听器被触发
unwatchAppList.value;
</script>

<style scoped>
/* 继承 AdminLayout 的样式 */
.v3-gradient-bg {
    background: linear-gradient(129deg, #f5fbff 11.03%, #fcfcfc 88.98%);
}

/* 自定义滚动条样式 */
nav {
    scrollbar-width: thin;
    scrollbar-color: #d4d4d4 transparent;
}

nav::-webkit-scrollbar {
    width: 6px;
}

nav::-webkit-scrollbar-track {
    background: transparent;
}

nav::-webkit-scrollbar-thumb {
    background-color: #d4d4d4;
    border-radius: 3px;
}
</style>
