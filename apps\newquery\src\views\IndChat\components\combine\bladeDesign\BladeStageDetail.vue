<!-- 指定部套的部套叶片详情 -->
<template>
  <div class="flex flex-1 flex-col">
    <div class="text-sm text-[#121619] mt-3 flex-shrink-0">叶片级号 STAGE</div>
    <div class="flex items-center justify-between mt-[10px] flex-shrink-0">
      <el-scrollbar ref="scrollbarRef">
        <div class="text-xs text-[#343A3F] flex w-full">
          <el-button
            class="!h-5 !px-2 !py-0 !text-xs flex items-center !rounded-md"
            v-for="(item, index) in currentPageItems"
            :key="index"
            :class="{
              '!text-[#343A3F]': selectedStageIndex !== currentPage * pageSize + index,
              '!text-[#129bfe] !bg-[#CFEAFE] !border-transparent':
                selectedStageIndex === currentPage * pageSize + index,
            }"
            @click="handleStageSelect(currentPage * pageSize + index)"
          >
            {{ item.stage }}
          </el-button>
        </div>
      </el-scrollbar>
    </div>
    <div
      class="flex flex-col text-sm text-[#121619] w-full blad-stage-bottom overflow-hidden flex-1"
    >
      <div class="flex px-3">
        <div
          class="mr-[36px] font-medium text-[14px] h-[40px] flex items-center py-2 px-0 cursor-pointer relative"
          :class="
            selectedBladeDataNav === 'basic'
              ? 'text-[#129BFE] font-medium'
              : 'text-gray-500 hover:text-gray-700'
          "
          @click="handleBladeDataNavChange('basic')"
        >
          叶片基本信息
          <div
            v-if="selectedBladeDataNav === 'basic'"
            class="absolute bottom-0 left-1/2 w-6 h-[2px] bg-[#129BFE] transform -translate-x-1/2 z-10"
          ></div>
        </div>
        <div
          class="mr-[36px] font-medium text-[14px] h-[40px] flex items-center py-2 px-0 cursor-pointer relative"
          :class="
            selectedBladeDataNav === 'shroud'
              ? 'text-[#129BFE] font-medium'
              : 'text-gray-500 hover:text-gray-700'
          "
          @click="handleBladeDataNavChange('shroud')"
        >
          叶片构成信息
          <div
            v-if="selectedBladeDataNav === 'shroud'"
            class="absolute bottom-0 left-1/2 w-6 h-[2px] bg-[#129BFE] transform -translate-x-1/2 z-10"
          ></div>
        </div>
        <div
          class="mr-[36px] font-medium text-[14px] h-[40px] flex items-center py-2 px-0 cursor-pointer relative"
          :class="
            selectedBladeDataNav === 'check'
              ? 'text-[#129BFE] font-medium'
              : 'text-gray-500 hover:text-gray-700'
          "
          @click="handleBladeDataNavChange('check')"
        >
          叶型检验信息
          <div
            v-if="selectedBladeDataNav === 'check'"
            class="absolute bottom-0 left-1/2 w-6 h-[2px] bg-[#129BFE] transform -translate-x-1/2 z-10"
          ></div>
        </div>
      </div>
      <div class="bg-[#f7f9fc] rounded-xl flex-1 p-5 border border-solid border-[#e5e5e5]">
        <template v-if="selectedBladeDataNav === 'basic'">
          <BladeBasicInfoDetail
            :blade-basic-data="selectedBladeData?.basic || []"
            :editable="editable"
            @update="handleBladeDataUpdate"
          />
        </template>
        <template v-if="selectedBladeDataNav === 'shroud'">
          <BladeComposeInfoDetail
            :blade-shroud-data="selectedBladeData?.shroud || []"
            :blade-profile-data="selectedBladeData?.profile || []"
            :blade-intermediate-data="selectedBladeData?.intermediate || []"
            :blade-root-data="selectedBladeData?.root || []"
            :editable="editable"
            :selected-compose-nav="selectedComposeNav"
            @update="handleBladeDataUpdate"
            @handle-compose-nav-change="handleComposeNavChange"
          />
        </template>
        <template v-if="selectedBladeDataNav === 'check'">
          <BladeCheckInfoDetail
            :blade-check-data="selectedBladeData?.check || []"
            :editable="editable"
            @update="handleBladeDataUpdate"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUpdated, onActivated } from 'vue';
// import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import BladeBasicInfoDetail from './bladeStage/BladeBasicInfoDetail.vue';
import BladeComposeInfoDetail from './bladeStage/BladeComposeInfoDetail.vue';
import BladeCheckInfoDetail from './bladeStage/BladeCheckInfoDetail.vue';

const props = defineProps({
  bladesStageList: {
    type: Array,
    required: true,
  },
  editable: {
    type: Boolean,
    required: true,
  },

  stageIndex: {
    // 叶片级号下标
    type: [Number, String],
    required: false,
  },
  selectedBladeDataNav: {
    // 叶片导航，基本信息、核验信息
    type: String,
    required: false,
  },
  selectedComposeNav: {
    // 叶片构成导航，围带、叶型、叶根
    type: String,
    required: false,
  },
});

const emit = defineEmits([
  'update',
  'handle-stage-change',
  'handle-compose-nav-change',
  'handle-blade-data-nav-change',
]);

// 分页相关
const pageSize = 18;
const currentPage = ref(0);
const selectedStageIndex = ref(0);
// 选中的叶片导航（基本信息、构成信息..）
const selectedBladeDataNav = ref('basic');
const selectedComposeNav = ref('shroud');
const scrollbarRef = ref(null); // 定义一个 ref 来引用 el-scrollbar
// 在页面加载时自动滚动到选中的 el-button 位置
onMounted(() => {
  initData();
});

// 初始化数据的方法
const initData = () => {
  // if (typeof props.stageIndex === 'number' || typeof props.stageIndex === 'string') {
  //   selectedStageIndex.value = Number(props.stageIndex);
  //   currentPage.value = Math.floor(Number(props.stageIndex) / pageSize);
  // }
  // selectedBladeDataNav.value = props.selectedBladeDataNav || 'basic';
  // selectedComposeNav.value = props.selectedComposeNav || 'shroud';

  nextTick(() => {
    nextTick(() => {
      if (scrollbarRef.value && selectedStageIndex.value !== null) {
        const buttonIndex = selectedStageIndex.value % pageSize;
        const buttonElement = scrollbarRef.value.$el.querySelector(
          `.el-button:nth-child(${buttonIndex + 1})`
        );
        if (buttonElement) {
          buttonElement.scrollIntoView({ behavior: 'auto', block: 'center' });
        }
      }
    });
  });
};

// 监听 stageIndex 的变化
watch(
  () => props.stageIndex,
  newValue => {
    // 修改判断条件，使用 typeof 来检查是否为数字
    if (typeof newValue === 'number' || typeof newValue === 'string') {
      selectedStageIndex.value = Number(newValue);
      currentPage.value = Math.floor(Number(newValue) / pageSize);
    }
  },
  { immediate: true }
);

// 监听父组件传来的bladesStageList数据变化
watch(
  () => props.bladesStageList,
  newValue => {
    // if (newValue && newValue.length > 0) {
    currentPage.value = 0;
    selectedStageIndex.value = props.stageIndex;
    // bladesStageList变化时，重置为basic
    selectedBladeDataNav.value = props.selectedBladeDataNav;
    selectedComposeNav.value = props.selectedComposeNav;

    // 强制更新 selectedBladeData
    nextTick(() => {
      // 触发重新计算
      selectedStageIndex.value = selectedStageIndex.value;
    });
    // }
  },
  { deep: true, immediate: true } //immediate设置为true，组件创建时立即执行一次
);

// 监听父组件传递的selectedBladeDataNav变化
watch(
  () => props.selectedBladeDataNav,
  newValue => {
    if (newValue !== undefined && newValue !== null) {
      selectedBladeDataNav.value = newValue;
    }
  },
  { immediate: true }
);

// 监听父组件传递的selectedComposeNav变化
watch(
  () => props.selectedComposeNav,
  newValue => {
    if (newValue !== undefined && newValue !== null) {
      selectedComposeNav.value = newValue;
    }
  },
  { immediate: true }
);

// 计算当前选中的叶片数据
const selectedBladeData = computed(() => {
  return props.bladesStageList[selectedStageIndex.value]?.blade_data || null;
});

// 计算当前页显示的叶片级号
const currentPageItems = computed(() => {
  return props.bladesStageList;
});

// 处理叶片级号选择
const handleStageSelect = index => {
  selectedStageIndex.value = index;
  selectedBladeDataNav.value = 'basic';
  selectedComposeNav.value = 'shroud';
  emit('handle-stage-change', index); // 新增
};

// 处理叶片导航变化，叶片基本信息or叶片构成信息or叶片核验参数
const handleBladeDataNavChange = nav => {
  selectedBladeDataNav.value = nav;
  emit('handle-blade-data-nav-change', selectedBladeDataNav.value);
};
// 监听叶片构成组件导航变化，同步修改当前组件中的selectedComposeNav
const handleComposeNavChange = nav => {
  selectedComposeNav.value = nav;
  emit('handle-compose-nav-change', selectedComposeNav.value);
};

// 处理基本信息更新
const handleBladeDataUpdate = item => {
  console.log('更新基本信息:', selectedStageIndex.value, item);
  // 找到当前选中的叶片数据
  const currentBlade = props.bladesStageList[selectedStageIndex.value];

  if (currentBlade) {
    // 发送更新事件到父组件
    emit('update', {
      type: currentBlade.type,
      stage: currentBlade.stage,
      key: item.key,
      value: item.value,
      bdid: props.bladesStageList[selectedStageIndex.value]?.id || null,
    });
  }
};
</script>

<style scoped>
:deep(.el-scrollbar) {
  padding-bottom: 12px;
}
</style>
