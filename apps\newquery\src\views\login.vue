<template>
  <div class="login-container">
    <div class="login-right" v-if="!currIsOkta">
      <div class="login-box">
        <img
          src="@/assets/login/login-icon1.png"
          alt=""
          style="height: 25px; margin-bottom: 40px"
        />
        <div class="font18 font-zhongcu" style="margin-bottom: 30px">用户登录</div>
        <form @submit.prevent="fetchhandleLogin">
          <div class="input-group">
            <div class="login-ipt-title font16">账号<span class="required-star">*</span></div>
            <input
              type="text"
              v-model="username"
              placeholder="输入邮箱地址"
              required
              class="font14"
            />
          </div>
          <div class="input-group" style="margin-bottom: 5px">
            <div class="login-ipt-title font16">密码<span class="required-star">*</span></div>
            <input
              type="password"
              v-model="password"
              placeholder="输入密码"
              required
              class="font14"
            />
          </div>
          <div class="password-tip font12">如果忘记密码，请找管理员找回或修改密码</div>
          <div style="height: 16px; margin-top: 5px">
            <div class="error-div font12" v-if="showErr">
              <img src="@/assets/login/error.png" alt="" class="error-img" />
              <span>{{ err_msg }}</span>
            </div>
          </div>
          <button
            type="submit"
            class="login-button login-button-no font18"
            :class="{ 'theme-background': isButtonRed }"
          >
            登录
          </button>
        </form>
        <div class="flex items-center justify-center mt-4">
          <el-checkbox v-model="privacyChecked" class="custom-checkbox">
            我已阅读并同意
            <a href="#" target="_blank" class="text-[#129bff] hover:text-blue-700">用户隐私政策</a>
          </el-checkbox>
        </div>
      </div>
    </div>
    <div class="login-right" v-if="currIsOkta">
      <span class="fixed left-[50%] top-[28%]">{{ oktaMsg }}</span>
      <!-- <div class="login-box"> -->
      <!-- <img
                    src="@/assets/login/login-icon1.png"
                    alt=""
                    style="height: 25px; margin-bottom: 40px"
                /> -->
      <!-- <div class="font18 font-zhongcu" style="margin-bottom: 30px">
                    用户登录
                </div> -->
      <!-- <div class="font16"></div> -->
      <!-- </div> -->

      <!-- 点击登录okta -->
      <!-- <button
                type="button"
                class="login-button login-button-no font18"
                :class="{ 'theme-background': isButtonRed }"
                @click="handleOktaLogin"
            >
                登录
            </button> -->
      <!-- 登出 -->
      <!-- <button
                type="button"
                class="login-button login-button-no font18"
                :class="{ 'theme-background': isButtonRed }"
                @click="handleOktaLogout"
            >
                登出
            </button> -->
      <!-- <div>
                {{ authState }}
            </div> -->
    </div>
  </div>
</template>
<script>
import { ref, computed, onMounted, inject } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getSiteuserSsoTokenAction } from '@/services/api';
import { loginApi, oktaLoginApi } from '@/services/intellido-api';
import { useUserStore } from '@/store/modules/userInfo';
import { ElMessage } from 'element-plus';
import { useConfig } from '@/hooks/useConfig';
import { useAuth } from '@okta/okta-vue';
import { isOktaMode } from '@/utils/auth';

export default {
  name: 'LoginView',

  setup() {
    const username = ref('');
    const password = ref('');
    const err_msg = ref(''); // 错误信息文字
    const showErr = ref(false);
    const router = useRouter();
    const route = useRoute();
    const userStore = useUserStore();
    const privacyChecked = ref(false);

    const currIsOkta = computed(() => isOktaMode());
    const oktaMsg = ref('');

    // 只在 Okta 模式下初始化 Okta 相关的功能
    let $auth = null;
    let authState = null;

    if (isOktaMode()) {
      $auth = useAuth();
      // 在Composition API中正确获取authState
      authState = inject('okta.authState');
    }

    const isLoggedIn = computed(() => userStore.isLoggedIn);
    const isButtonRed = computed(() => username.value && password.value);

    onMounted(async () => {
      // 检查当前是否 okta
      if (currIsOkta.value) {
        // 检查是否有logout参数，如果有则执行登出
        if (route.query.logout) {
          await handleOktaLogout();
          return;
        }

        // debugger;
        // const isAuthenticated1 = authState.value.isAuthenticated;
        // const isAuthenticated = await $auth.isAuthenticated();

        oktaMsg.value = '检查登录状态...';
        const token = localStorage.getItem('token');
        if (token) {
          // 检查 token 是否有效
          const userInfo = await userStore.fetchUserInfo();
          if (userInfo) {
            const redirectPath = localStorage.getItem('redirectAfterLogin');
            // 获取全局配置中的登录重定向URL
            const { globalConfig } = useConfig();

            if (redirectPath) {
              router.push(redirectPath);
              localStorage.removeItem('redirectAfterLogin');
            } else if (globalConfig.loginRedirectUrl) {
              // 如果配置了登录重定向URL，则使用window.location.href跳转
              window.location.href = globalConfig.loginRedirectUrl;
            } else {
              router.push({ name: 'Index' });
            }
          } else {
            // token 无效，清除它
            localStorage.removeItem('token');

            // 执行okta登录
            handleOktaLogin();
          }
        } else {
          // 执行okta登录
          handleOktaLogin();
        }
      }
    });

    const handleOktaLogin = async () => {
      console.log('执行okta登录');

      if (!$auth) {
        console.error('Okta Auth 未初始化');
        oktaMsg.value = 'Okta 认证未初始化';
        return;
      }

      try {
        // debugger;
        oktaMsg.value = '正在验证信息...';
        // const isAuthenticated1 = authState.value.isAuthenticated;
        const isAuthenticated = await $auth.isAuthenticated();
        if (isAuthenticated) {
          // const claims = await $auth.getUser();
          // console.log('claims', claims);
          // 如果已经认证，可以直接跳转或处理用户信息
          // 这里可以根据需要处理已认证的逻辑

          // 获取 id_token
          const idToken = await $auth.getIdToken();
          console.log('idToken', idToken);

          // 获取 nonce
          const getTokensRes = await $auth.tokenManager.getTokens();
          console.log('getTokensRes', getTokensRes);

          const nonce = getTokensRes?.idToken?.claims?.nonce;
          console.log('nonce', nonce);

          oktaMsg.value = '登录中...';

          // 执行本地真正登录，调用后的返回处理跟fetchhandleLogin里面的返回处理一致
          try {
            const response = await oktaLoginApi({
              id_token: idToken,
              nonce: nonce,
            });

            if (response.data.error) {
              oktaMsg.value = response.data.message || '登录失败';
            } else {
              const token = response.data.access_token;
              localStorage.setItem('token', token);
              localStorage.setItem('console_token', token);
              // refresh_token
              localStorage.setItem('refresh_token', token);

              localStorage.removeItem('expiry_notice_hidden');

              // 登录成功后获取用户信息
              await userStore.fetchUserInfo();

              // 检查是否成功获取用户信息
              if (userStore.isLoggedIn) {
                const redirectPath = localStorage.getItem('redirectAfterLogin');
                // 获取全局配置中的登录重定向URL
                const { globalConfig } = useConfig();

                if (redirectPath) {
                  router.push(redirectPath);
                  localStorage.removeItem('redirectAfterLogin');
                } else if (globalConfig.loginRedirectUrl) {
                  // 如果配置了登录重定向URL，则使用window.location.href跳转
                  window.location.href = globalConfig.loginRedirectUrl;
                } else {
                  router.push({ name: 'Index' });
                }
              } else {
                oktaMsg.value = '获取用户信息失败';
              }
            }
          } catch (oktaLoginError) {
            console.error('OKTA登录API调用失败:', oktaLoginError);
            oktaMsg.value = '登录失败，请重试';
          }
        } else {
          oktaMsg.value = '正在登录...';

          // 执行Okta登录重定向
          await $auth.signInWithRedirect({
            originalUri: '/',
          });
        }
      } catch (error) {
        console.error('Okta登录过程中出错:', error);
        oktaMsg.value = '登录失败，请重试';
      }
    };

    const handleOktaLogout = async () => {
      if (!$auth) {
        console.error('Okta Auth 未初始化');
        return;
      }

      try {
        oktaMsg.value = '正在登出...';
        await $auth.signOut();
        oktaMsg.value = '登出成功';
        // localStorage.removeItem('token');
        // localStorage.removeItem('console_token');
        // localStorage.removeItem('refresh_token');
        // localStorage.removeItem('expiry_notice_hidden');
        // router.push({ name: 'Login' });
      } catch (error) {
        console.error('Okta登出过程中出错:', error);
        oktaMsg.value = '登出失败，请重试';
      }
    };

    const fetchhandleLogin = async () => {
      if (!privacyChecked.value) {
        ElMessage({
          message: '请先阅读并同意用户隐私政策',
          type: 'error',
          duration: 3000,
        });
        return;
      }

      try {
        const response = await loginApi({
          username: username.value.trim(),
          password: password.value.trim(),
        });

        console.log('登录成功--------', response);

        // debugger;
        if (response.data.error) {
          showErr.value = true;
          err_msg.value = response.data.message;
        } else {
          showErr.value = false;
          err_msg.value = '';
          const token = response.data.access_token;
          localStorage.setItem('token', token);
          //   debugger;
          localStorage.setItem('console_token', token);
          // refresh_token
          localStorage.setItem('refresh_token', token);

          localStorage.removeItem('expiry_notice_hidden');

          // if (route.query.sso == '1') {
          //     getSsoTokenToOtherSystems(route.query.sso, token, 0);
          // } else {
          // 登录成功后获取用户信息
          await userStore.fetchUserInfo();

          // 检查是否成功获取用户信息
          if (isLoggedIn.value) {
            const redirectPath = localStorage.getItem('redirectAfterLogin');
            // 获取全局配置中的登录重定向URL
            const { globalConfig } = useConfig();

            if (redirectPath) {
              router.push(redirectPath);
              localStorage.removeItem('redirectAfterLogin');
            } else if (globalConfig.loginRedirectUrl) {
              // 如果配置了登录重定向URL，则使用window.location.href跳转
              window.location.href = globalConfig.loginRedirectUrl;
            } else {
              router.push({ name: 'Index' });
            }
          } else {
            showErr.value = true;
            err_msg.value = '获取用户信息失败';
          }
          // }
        }
      } catch (error) {
        console.error('发送消息失败:', error);
        showErr.value = true;
        err_msg.value = '登录失败，请重试';
      }
    };

    const getSsoTokenToOtherSystems = async (sso, token, autologin) => {
      if (token) {
        try {
          // 如果已经登录，获取sso_token,并跳转回其他系统
          const response = await getSiteuserSsoTokenAction(sso);
          if (response.data.sso_token) {
            const sso_token = response.data.sso_token;
            if (route.query.ssourl) {
              if (route.query.islogout == 1) {
                if (!autologin) {
                  const urlWithToken = `${decodeURIComponent(
                    route.query.ssourl
                  )}?sso_token=${encodeURIComponent(sso_token)}`;
                  // 使用window.location.href进行跳转
                  window.location.href = urlWithToken;
                }
              } else {
                const urlWithToken = `${decodeURIComponent(
                  route.query.ssourl
                )}?sso_token=${encodeURIComponent(sso_token)}`;
                // 使用window.location.href进行跳转
                window.location.href = urlWithToken;
              }
            }
          }
        } catch (error) {
          console.log(error);
        }
      }
    };

    return {
      username,
      password,
      err_msg,
      showErr,
      isButtonRed,
      fetchhandleLogin,
      privacyChecked,
      currIsOkta,
      oktaMsg,
      handleOktaLogin,
      handleOktaLogout,
    };
  },
};
</script>
<style scoped>
.login-container {
  width: 100%;
  height: 100vh;
  background: #f5faff;
  background-image: url('@/assets/login/login-bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: left center;
}
.login-box {
  width: 500px;
  background: #fff;
  padding: 30px;
  border-radius: 15px;
}
.login-ipt-title {
  color: #666;
  margin-bottom: 10px;
}
.login-right {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  float: right;
  margin-right: 140px;
}
.input-group {
  margin-bottom: 20px;
  width: 100%;
}
.input-group:nth-of-type(2) {
  margin-bottom: 15px;
}
.input-group input {
  width: 100%;
  padding: 15px 20px;
  background: #f7f7f7 !important;
  border: none;
  border-radius: 10px;
  overflow: hidden;
}
input::placeholder {
  color: #d9d9d9;
}
.input-group input:focus {
  background: #f7f7f7 !important;
}
.login-button {
  width: 100%;
  height: 55px;
  padding: 10px;
  color: #fff;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  margin-top: 25px;
  background: #129bff;
}
.agreement,
.contact {
  margin-top: 10px !important;
  text-align: center;
  color: #666;
  margin-bottom: 0;
}
.error-div {
  color: #ff0000;
  display: flex;
  align-items: center;
}
.error-img {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

@media (min-width: 2000px) {
  .login-box {
    width: 500px;
  }
  .login-right {
    margin-right: 12vw;
  }
}

/* 响应式设计 */
@media (max-width: 1600px) and (min-width: 800px) {
  .login-box {
    width: 33vw;
  }
  .login-right {
    margin-right: 6vw;
  }
}

:deep(.custom-checkbox) {
  --el-checkbox-font-size: 14px;
  --el-checkbox-text-color: #666;
}

:deep(.el-checkbox__label) {
  color: #666;
}

:deep(.el-checkbox__inner) {
  width: 16px;
  height: 16px;
  border-radius: 4.8px;
}

:deep(.el-checkbox__inner::after) {
  height: 8px;
  left: 5px;
  top: 2px;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #129bff;
  border-color: #129bff;
}

:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #666;
}

:deep(.el-checkbox__inner:hover) {
  border-color: #129bff;
}

:deep(.el-checkbox__input.is-focus .el-checkbox__inner) {
  border-color: #129bff;
}

.required-star {
  color: #ff0000;
  margin-left: 2px;
}

.password-tip {
  color: #666;
  margin-bottom: 10px;
}
</style>
